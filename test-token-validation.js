// Simple test script to validate JWT token format
const crypto = require('crypto');

// Simulate JWT token creation (similar to backend)
function createTestJWT() {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  const payload = {
    userId: 'test-user-id',
    email: '<EMAIL>',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    jti: crypto.randomUUID(),
    fingerprint: crypto.randomBytes(16).toString('hex')
  };
  
  const secret = 'keyyyyy'; // Default secret
  
  // Create JWT manually (simplified)
  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64url');
  
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Test JWT structure validation
function validateJWTStructure(token) {
  console.log('🔍 Testing JWT Structure Validation');
  console.log('Token:', token);
  
  // Check if token has 3 parts
  const parts = token.split('.');
  console.log('Parts count:', parts.length);
  
  if (parts.length !== 3) {
    console.log('❌ Invalid: Token must have 3 parts');
    return false;
  }
  
  try {
    // Decode header
    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    console.log('Header:', header);
    
    // Decode payload
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    console.log('Payload:', payload);
    
    // Basic validation
    if (!header.alg || !payload.exp || !payload.iat) {
      console.log('❌ Invalid: Missing required fields');
      return false;
    }
    
    console.log('✅ Valid JWT structure');
    return true;
  } catch (error) {
    console.log('❌ Invalid: Failed to decode JWT parts');
    console.error(error.message);
    return false;
  }
}

// Test encryption/decryption (if needed)
function testEncryption() {
  console.log('\n🔐 Testing Token Encryption (Optional)');
  
  const ENCRYPTION_KEY = 'keyyyyy'.slice(0, 32).padEnd(32, '0');
  const IV_LENGTH = 16;
  
  function encryptToken(token) {
    try {
      const iv = crypto.randomBytes(IV_LENGTH);
      const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
      let encrypted = cipher.update(token, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      return token;
    }
  }
  
  function decryptToken(encryptedToken) {
    try {
      if (!encryptedToken.includes(':')) {
        return encryptedToken;
      }
      
      const [ivHex, encrypted] = encryptedToken.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedToken;
    }
  }
  
  const testToken = createTestJWT();
  console.log('Original token:', testToken);
  
  const encrypted = encryptToken(testToken);
  console.log('Encrypted token:', encrypted);
  
  const decrypted = decryptToken(encrypted);
  console.log('Decrypted token:', decrypted);
  
  console.log('Encryption/Decryption test:', testToken === decrypted ? '✅ Passed' : '❌ Failed');
}

// Run tests
console.log('🧪 JWT Token Validation Tests\n');

// Test 1: Create and validate JWT
const testJWT = createTestJWT();
validateJWTStructure(testJWT);

// Test 2: Test invalid tokens
console.log('\n🔍 Testing Invalid Tokens');
validateJWTStructure('invalid.token'); // Only 2 parts
validateJWTStructure('invalid.token.structure.extra'); // 4 parts
validateJWTStructure('not-base64.not-base64.not-base64'); // Invalid base64

// Test 3: Test encryption (optional)
testEncryption();

console.log('\n✅ All tests completed!');
console.log('\n📋 Summary:');
console.log('- JWT tokens should be plain (not encrypted) for API calls');
console.log('- Backend middleware expects plain JWT with Bearer prefix');
console.log('- Session data (user info) can be encrypted for storage');
console.log('- Token structure: header.payload.signature');
