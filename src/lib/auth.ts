import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";
import crypto from "crypto";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  accessToken: string;
  refreshToken?: string;
  image?: string;
  oauthProvider?: string;
  oauthId?: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken?: string;
    user: User;
    expiresAt: number;
  };
}

// Security utilities
const ENCRYPTION_KEY = process.env.NEXTAUTH_SECRET?.slice(0, 32).padEnd(32, '0') || 'default-key-32-chars-long-here';
const IV_LENGTH = 16;

const encryptData = (data: string): string => {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error("Data encryption failed:", error);
    return data; // Fallback to plain data if encryption fails
  }
};

const decryptData = (encryptedData: string): string => {
  try {
    if (!encryptedData || !encryptedData.includes(':')) {
      return encryptedData; // Return as-is if not encrypted
    }

    const [ivHex, encrypted] = encryptedData.split(':');
    if (!ivHex || !encrypted) {
      return encryptedData;
    }

    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error("Data decryption failed:", error);
    return encryptedData; // Return as-is if decryption fails
  }
};

// Encrypt entire session data
const encryptSessionData = (data: any): string => {
  try {
    const jsonString = JSON.stringify(data);
    return encryptData(jsonString);
  } catch (error) {
    console.error("Session encryption failed:", error);
    return JSON.stringify(data);
  }
};

// Decrypt entire session data
const decryptSessionData = (encryptedData: string): any => {
  try {
    const decryptedString = decryptData(encryptedData);
    return JSON.parse(decryptedString);
  } catch (error) {
    console.error("Session decryption failed:", error);
    try {
      return JSON.parse(encryptedData);
    } catch {
      return null;
    }
  }
};

const createTokenFingerprint = (token: string, userAgent?: string): string => {
  const data = token + (userAgent || '') + process.env.NEXTAUTH_SECRET;
  return crypto.createHash('sha256').update(data).digest('hex').slice(0, 16);
};

const validateTokenFingerprint = (token: string, fingerprint: string, userAgent?: string): boolean => {
  const expectedFingerprint = createTokenFingerprint(token, userAgent);
  return crypto.timingSafeEqual(
    Buffer.from(fingerprint, 'hex'),
    Buffer.from(expectedFingerprint, 'hex')
  );
};

// Helper function to refresh user profile from API
async function refreshUserProfile(accessToken: string): Promise<User | null> {
  try {
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (data.status && data.data) {
      return data.data.user;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh user profile:", error);
    return null;
  }
}

// Helper function to refresh access token
async function refreshAccessToken(refreshToken: string): Promise<APIResponse | null> {
  try {
    const { data } = await axios.post<APIResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
      {
        refreshToken,
      }
    );

    if (data.status) {
      return data;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh access token:", error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          scope: "openid email profile",
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            email: data.data.user.email,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
            image: data.data.user.image,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        try {
          // Send Google user data to our API
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
            {
              email: user.email,
              name: user.name,
              image: user.image,
              googleId: user.id,
              accessToken: account.access_token,
            }
          );

          if (data.status && data.data) {
            // Update user object with API response
            user.id = data.data.user.id;
            user.firstName = data.data.user.firstName;
            user.lastName = data.data.user.lastName;
            user.accessToken = data.data.accessToken;
            user.refreshToken = data.data.refreshToken;
            return true;
          }
          return false;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, trigger }) {
      // Initial sign in
      if (user) {
        // Store raw tokens for API calls, encrypt user data for session
        const userData = {
          id: user.id,
          firstName: user.firstName || "",
          lastName: user.lastName || "",
          email: user.email!,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
        };

        // Encrypt user data but keep tokens as-is for API calls
        const encryptedUserData = encryptSessionData(userData);
        const tokenFingerprint = createTokenFingerprint(user.accessToken);

        token.userData = encryptedUserData;
        token.accessToken = user.accessToken; // Keep plain for API calls
        token.refreshToken = user.refreshToken;
        token.expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
        token.fingerprint = tokenFingerprint;
      }

      // Validate token fingerprint for security
      if (token.accessToken && token.fingerprint) {
        if (!validateTokenFingerprint(token.accessToken as string, token.fingerprint as string)) {
          console.warn("Token fingerprint validation failed");
          return {
            ...token,
            accessToken: "",
            refreshToken: "",
            expiresAt: 0,
            userData: ""
          };
        }
      }

      // Refresh user profile from API on session load/update (not on every request)
      if (token.accessToken && trigger === "update") {
        try {
          console.log("🔄 Fetching fresh profile data from API...");
          const updatedProfile = await refreshUserProfile(token.accessToken as string);
          if (updatedProfile) {
            // Always update with fresh data from API
            const updatedUserData = {
              id: updatedProfile.id,
              firstName: updatedProfile.firstName,
              lastName: updatedProfile.lastName,
              email: updatedProfile.email,
              phoneNumber: updatedProfile.phoneNumber,
              image: updatedProfile.image,
              oauthProvider: updatedProfile.oauthProvider,
              oauthId: updatedProfile.oauthId,
            };
            token.userData = encryptSessionData(updatedUserData);

            console.log("✅ Profile updated from API:", {
              firstName: updatedProfile.firstName,
              lastName: updatedProfile.lastName,
              email: updatedProfile.email,
              phoneNumber: updatedProfile.phoneNumber
            });
          }
        } catch (error) {
          console.error("❌ Failed to refresh profile:", error);
          // If profile refresh fails, keep existing data but log the error
        }
      }

      // Refresh access token if expired
      if (token.expiresAt && Date.now() >= token.expiresAt && token.refreshToken) {
        try {
          const refreshedToken = await refreshAccessToken(token.refreshToken as string);
          if (refreshedToken?.data) {
            // Update tokens and fingerprint
            const newFingerprint = createTokenFingerprint(refreshedToken.data.accessToken);

            token.accessToken = refreshedToken.data.accessToken;
            token.refreshToken = refreshedToken.data.refreshToken;
            token.expiresAt = refreshedToken.data.expiresAt;
            token.fingerprint = newFingerprint;
            return token;
          }
        } catch (error) {
          console.error("Failed to refresh token:", error);
        }
        // Token refresh failed, clear token data
        return {
          ...token,
          accessToken: "",
          refreshToken: "",
          expiresAt: 0,
          fingerprint: "",
          userData: ""
        };
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        // Decrypt user data for session
        const userData = token.userData ? decryptSessionData(token.userData as string) : null;

        if (userData) {
          session.user.id = userData.id;
          session.user.name = `${userData.firstName} ${userData.lastName}`;
          session.user.email = userData.email;
          session.user.firstName = userData.firstName;
          session.user.lastName = userData.lastName;
          session.user.phoneNumber = userData.phoneNumber;
          session.user.image = userData.image;
          session.user.oauthProvider = userData.oauthProvider;
          session.user.oauthId = userData.oauthId;
        }

        // Provide tokens for API calls (these are not encrypted)
        session.accessToken = token.accessToken as string;
        session.refreshToken = token.refreshToken as string;
        session.expiresAt = token.expiresAt as number;
      }
      return session;
    },
  },
};