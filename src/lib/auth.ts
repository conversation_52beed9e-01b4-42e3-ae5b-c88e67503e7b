import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  accessToken: string;
  refreshToken?: string;
  image?: string;
  oauthProvider?: string;
  oauthId?: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken?: string;
    user: User;
    expiresAt: number;
  };
}

// Helper function to refresh user profile from API
async function refreshUserProfile(accessToken: string): Promise<User | null> {
  try {
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (data.status && data.data) {
      return data.data.user;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh user profile:", error);
    return null;
  }
}

// Helper function to refresh access token
async function refreshAccessToken(refreshToken: string): Promise<APIResponse | null> {
  try {
    const { data } = await axios.post<APIResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
      {
        refreshToken,
      }
    );

    if (data.status) {
      return data;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh access token:", error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          scope: "openid email profile",
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            email: data.data.user.email,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
            image: data.data.user.image,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        try {
          // Send Google user data to our API
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
            {
              email: user.email,
              name: user.name,
              image: user.image,
              googleId: user.id,
              accessToken: account.access_token,
            }
          );

          if (data.status && data.data) {
            // Update user object with API response
            user.id = data.data.user.id;
            user.firstName = data.data.user.firstName;
            user.lastName = data.data.user.lastName;
            user.accessToken = data.data.accessToken;
            user.refreshToken = data.data.refreshToken;
            return true;
          }
          return false;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, trigger }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.email = user.email!;
        token.firstName = user.firstName || "";
        token.lastName = user.lastName || "";
        token.phoneNumber = user.phoneNumber;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token.image = user.image;
        token.oauthProvider = user.oauthProvider;
        token.oauthId = user.oauthId;
        token.expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      }

      // Refresh user profile on each request if token is still valid
      if (trigger === "update" || (token.expiresAt && Date.now() < token.expiresAt)) {
        try {
          const updatedProfile = await refreshUserProfile(token.accessToken);
          if (updatedProfile) {
            token.firstName = updatedProfile.firstName;
            token.lastName = updatedProfile.lastName;
            token.phoneNumber = updatedProfile.phoneNumber;
            token.image = updatedProfile.image;
          }
        } catch (error) {
          console.error("Failed to refresh profile:", error);
        }
      }

      // Refresh access token if expired
      if (token.expiresAt && Date.now() >= token.expiresAt && token.refreshToken) {
        try {
          const refreshedToken = await refreshAccessToken(token.refreshToken);
          if (refreshedToken?.data) {
            token.accessToken = refreshedToken.data.accessToken;
            token.refreshToken = refreshedToken.data.refreshToken;
            token.expiresAt = refreshedToken.data.expiresAt;
            return token;
          }
        } catch (error) {
          console.error("Failed to refresh token:", error);
        }
        // Token refresh failed, clear token data
        return {
          ...token,
          accessToken: "",
          refreshToken: "",
          expiresAt: 0
        };
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.id;
        session.user.name = `${token.firstName} ${token.lastName}`;
        session.user.email = token.email;
        session.user.firstName = token.firstName;
        session.user.lastName = token.lastName;
        session.user.phoneNumber = token.phoneNumber;
        session.user.image = token.image;
        session.accessToken = token.accessToken;
        session.refreshToken = token.refreshToken;
        session.expiresAt = token.expiresAt;
      }
      return session;
    },
  },
};