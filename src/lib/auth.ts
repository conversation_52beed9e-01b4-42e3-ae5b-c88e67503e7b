import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  accessToken: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    user: User;
    expitedAt: number;
  };
}

interface Token extends Record<string, any> {
  id: string;
  accessToken: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
}

interface Session extends Record<string, any> {
  user: {
    id: string;
    name?: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
  };
  accessToken: string;
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            email: data.data.user.email,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async jwt({ token, user }: { token: Token; user?: User }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
        token.phoneNumber = user.phoneNumber;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: Token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.name = `${token.firstName} ${token.lastName}`;
        session.user.email = token.email;
        session.user.firstName = token.firstName;
        session.user.lastName = token.lastName;
        session.user.phoneNumber = token.phoneNumber;
        session.accessToken = token.accessToken;
      }
      return session;
    },
  },
};