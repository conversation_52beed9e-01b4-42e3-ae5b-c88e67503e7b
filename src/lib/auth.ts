import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";
import crypto from "crypto";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  accessToken: string;
  refreshToken?: string;
  image?: string;
  oauthProvider?: string;
  oauthId?: string;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken?: string;
    user: User;
    expiresAt: number;
  };
}

// Security utilities
const ENCRYPTION_KEY = process.env.NEXTAUTH_SECRET?.slice(0, 32).padEnd(32, '0') || 'default-key-32-chars-long-here';
const IV_LENGTH = 16;

const encryptToken = (token: string): string => {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error("Token encryption failed:", error);
    return token; // Fallback to plain token if encryption fails
  }
};

const decryptToken = (encryptedToken: string): string => {
  try {
    if (!encryptedToken.includes(':')) {
      return encryptedToken; // Return as-is if not encrypted
    }

    const [ivHex, encrypted] = encryptedToken.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error("Token decryption failed:", error);
    return encryptedToken; // Return as-is if decryption fails
  }
};

const createTokenFingerprint = (token: string, userAgent?: string): string => {
  const data = token + (userAgent || '') + process.env.NEXTAUTH_SECRET;
  return crypto.createHash('sha256').update(data).digest('hex').slice(0, 16);
};

const validateTokenFingerprint = (token: string, fingerprint: string, userAgent?: string): boolean => {
  const expectedFingerprint = createTokenFingerprint(token, userAgent);
  return crypto.timingSafeEqual(
    Buffer.from(fingerprint, 'hex'),
    Buffer.from(expectedFingerprint, 'hex')
  );
};

// Helper function to refresh user profile from API
async function refreshUserProfile(accessToken: string): Promise<User | null> {
  try {
    const { data } = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (data.status && data.data) {
      return data.data.user;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh user profile:", error);
    return null;
  }
}

// Helper function to refresh access token
async function refreshAccessToken(refreshToken: string): Promise<APIResponse | null> {
  try {
    const { data } = await axios.post<APIResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
      {
        refreshToken,
      }
    );

    if (data.status) {
      return data;
    }
    return null;
  } catch (error) {
    console.error("Failed to refresh access token:", error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          scope: "openid email profile",
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: credentials.email,
              password: credentials.password,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          return {
            id: data.data.user.id,
            firstName: data.data.user.firstName,
            lastName: data.data.user.lastName,
            email: data.data.user.email,
            phoneNumber: data.data.user.phoneNumber,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
            image: data.data.user.image,
          };
        } catch (error) {
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        try {
          // Send Google user data to our API
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
            {
              email: user.email,
              name: user.name,
              image: user.image,
              googleId: user.id,
              accessToken: account.access_token,
            }
          );

          if (data.status && data.data) {
            // Update user object with API response
            user.id = data.data.user.id;
            user.firstName = data.data.user.firstName;
            user.lastName = data.data.user.lastName;
            user.accessToken = data.data.accessToken;
            user.refreshToken = data.data.refreshToken;
            return true;
          }
          return false;
        } catch (error) {
          console.error("Google sign-in error:", error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, trigger }) {
      // Initial sign in
      if (user) {
        // Encrypt sensitive tokens
        const encryptedAccessToken = encryptToken(user.accessToken);
        const encryptedRefreshToken = user.refreshToken ? encryptToken(user.refreshToken) : undefined;

        // Create fingerprint for token validation
        const tokenFingerprint = createTokenFingerprint(user.accessToken);

        token.id = user.id;
        token.email = user.email!;
        token.firstName = user.firstName || "";
        token.lastName = user.lastName || "";
        token.phoneNumber = user.phoneNumber;
        token.accessToken = encryptedAccessToken;
        token.refreshToken = encryptedRefreshToken;
        token.image = user.image;
        token.oauthProvider = user.oauthProvider;
        token.oauthId = user.oauthId;
        token.expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
        token.fingerprint = tokenFingerprint;
      }

      // Validate token fingerprint for security
      if (token.accessToken && token.fingerprint) {
        const decryptedToken = decryptToken(token.accessToken as string);
        if (!validateTokenFingerprint(decryptedToken, token.fingerprint as string)) {
          console.warn("Token fingerprint validation failed");
          return {
            ...token,
            accessToken: "",
            refreshToken: "",
            expiresAt: 0
          };
        }
      }

      // Refresh user profile on each request if token is still valid
      if (trigger === "update" || (token.expiresAt && Date.now() < token.expiresAt)) {
        try {
          const decryptedAccessToken = decryptToken(token.accessToken as string);
          const updatedProfile = await refreshUserProfile(decryptedAccessToken);
          if (updatedProfile) {
            token.firstName = updatedProfile.firstName;
            token.lastName = updatedProfile.lastName;
            token.phoneNumber = updatedProfile.phoneNumber;
            token.image = updatedProfile.image;
          }
        } catch (error) {
          console.error("Failed to refresh profile:", error);
        }
      }

      // Refresh access token if expired
      if (token.expiresAt && Date.now() >= token.expiresAt && token.refreshToken) {
        try {
          const decryptedRefreshToken = decryptToken(token.refreshToken as string);
          const refreshedToken = await refreshAccessToken(decryptedRefreshToken);
          if (refreshedToken?.data) {
            // Re-encrypt new tokens
            const newEncryptedAccessToken = encryptToken(refreshedToken.data.accessToken);
            const newEncryptedRefreshToken = refreshedToken.data.refreshToken ?
              encryptToken(refreshedToken.data.refreshToken) : undefined;
            const newFingerprint = createTokenFingerprint(refreshedToken.data.accessToken);

            token.accessToken = newEncryptedAccessToken;
            token.refreshToken = newEncryptedRefreshToken;
            token.expiresAt = refreshedToken.data.expiresAt;
            token.fingerprint = newFingerprint;
            return token;
          }
        } catch (error) {
          console.error("Failed to refresh token:", error);
        }
        // Token refresh failed, clear token data
        return {
          ...token,
          accessToken: "",
          refreshToken: "",
          expiresAt: 0,
          fingerprint: ""
        };
      }

      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        // Decrypt tokens for session use
        const decryptedAccessToken = token.accessToken ? decryptToken(token.accessToken as string) : "";
        const decryptedRefreshToken = token.refreshToken ? decryptToken(token.refreshToken as string) : "";

        session.user.id = token.id;
        session.user.name = `${token.firstName} ${token.lastName}`;
        session.user.email = token.email;
        session.user.firstName = token.firstName;
        session.user.lastName = token.lastName;
        session.user.phoneNumber = token.phoneNumber;
        session.user.image = token.image;
        session.accessToken = decryptedAccessToken;
        session.refreshToken = decryptedRefreshToken;
        session.expiresAt = token.expiresAt;
      }
      return session;
    },
  },
};