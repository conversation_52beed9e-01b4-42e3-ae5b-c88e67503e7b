import type { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import axios from "axios";
import crypto from "crypto";

// Security utilities
const RATE_LIMIT_MAP = new Map<string, { count: number; resetTime: number }>();

const checkRateLimit = (identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean => {
  const now = Date.now();
  const record = RATE_LIMIT_MAP.get(identifier);

  if (!record || now > record.resetTime) {
    RATE_LIMIT_MAP.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxAttempts) {
    return false;
  }

  record.count++;
  return true;
};

const sanitizeInput = (input: string): string => {
  return input.trim().toLowerCase();
};

const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

const createSecureHeaders = (token?: string) => ({
  'Content-Type': 'application/json',
  'User-Agent': 'NextAuth-Client/1.0',
  'X-Requested-With': 'XMLHttpRequest',
  ...(token && { 'Authorization': `Bearer ${token}` }),
});

// Interfaces
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  image?: string;
  oauthProvider?: string;
  oauthId?: string;
  isActive?: boolean;
  isBlocked?: boolean;
}

interface APIResponse {
  status: boolean;
  message: string;
  data: {
    accessToken: string;
    refreshToken?: string;
    user: User;
    expiresAt: number;
  };
}

interface ProfileResponse {
  status: boolean;
  message: string;
  data: {
    user: User;
  };
}

// Fetch user profile from API
const fetchUserProfile = async (accessToken: string): Promise<User | null> => {
  try {
    console.log('🔄 Fetching user profile from API...');

    const { data } = await axios.get<ProfileResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
      {
        headers: createSecureHeaders(accessToken),
        timeout: 10000, // 10 second timeout
      }
    );

    if (data.status && data.data?.user) {
      console.log('✅ Profile fetched successfully:', data.data.user.email);
      return data.data.user;
    }

    console.warn('❌ Invalid profile response:', data);
    return null;
  } catch (error) {
    console.error('❌ Failed to fetch profile:', error);
    return null;
  }
};

// Google OAuth handler
const handleGoogleOAuth = async (account: any, profile: any): Promise<User | null> => {
  try {
    console.log('🔄 Processing Google OAuth...');

    const { data } = await axios.post<APIResponse>(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/google`,
      {
        email: profile.email,
        name: profile.name,
        image: profile.picture,
        googleId: profile.sub,
        accessToken: account.access_token,
      },
      {
        headers: createSecureHeaders(),
        timeout: 10000,
      }
    );

    if (data.status && data.data?.user) {
      console.log('✅ Google OAuth successful:', data.data.user.email);
      return {
        ...data.data.user,
        accessToken: data.data.accessToken,
      } as User & { accessToken: string };
    }

    console.warn('❌ Google OAuth failed:', data);
    return null;
  } catch (error) {
    console.error('❌ Google OAuth error:', error);
    return null;
  }
};

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
        },
      },
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required");
        }

        // Rate limiting check
        const clientId = credentials.email;
        if (!checkRateLimit(clientId)) {
          throw new Error("Too many login attempts. Please try again later.");
        }

        // Input validation
        const email = sanitizeInput(credentials.email);
        if (!validateEmail(email)) {
          throw new Error("Invalid email format");
        }

        try {
          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
            {
              emailPhoneNumber: email,
              password: credentials.password,
            },
            {
              headers: createSecureHeaders(),
              timeout: 10000,
            }
          );

          if (!data.status || !data.data) {
            throw new Error(data.message || "Invalid credentials");
          }

          // Return user with access token
          return {
            id: data.data.user.id,
            email: data.data.user.email,
            name: `${data.data.user.firstName} ${data.data.user.lastName}`,
            accessToken: data.data.accessToken,
            refreshToken: data.data.refreshToken,
            expiresAt: data.data.expiresAt,
          };
        } catch (error) {
          console.error('❌ Credentials auth failed:', error);
          if (axios.isAxiosError(error)) {
            const message = error.response?.data?.message || "Authentication failed";
            throw new Error(message);
          }
          throw new Error("Authentication failed");
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        if (account?.provider === "google" && profile) {
          // Handle Google OAuth
          const googleUser = await handleGoogleOAuth(account, profile);
          if (googleUser) {
            // Merge Google user data into user object
            Object.assign(user, {
              accessToken: (googleUser as any).accessToken,
              refreshToken: (googleUser as any).refreshToken,
            });
            return true;
          }
          return false;
        }

        // For credentials provider, user is already validated
        return true;
      } catch (error) {
        console.error('❌ SignIn callback error:', error);
        return false;
      }
    },

    async jwt({ token, user }) {
      // Initial sign in - store only tokens
      if (user) {
        token.accessToken = (user as any).accessToken;
        token.refreshToken = (user as any).refreshToken;
        token.expiresAt = (user as any).expiresAt || Date.now() + (24 * 60 * 60 * 1000);

        console.log('✅ JWT callback - tokens stored');
      }

      // Check if token is expired and refresh if needed
      if (token.expiresAt && Date.now() >= (token.expiresAt as number) && token.refreshToken) {
        try {
          console.log('🔄 Refreshing expired token...');

          const { data } = await axios.post<APIResponse>(
            `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
            { refreshToken: token.refreshToken },
            {
              headers: createSecureHeaders(),
              timeout: 10000,
            }
          );

          if (data.status && data.data) {
            token.accessToken = data.data.accessToken;
            token.refreshToken = data.data.refreshToken;
            token.expiresAt = data.data.expiresAt;

            console.log('✅ Token refreshed successfully');
            return token;
          } else {
            console.warn('❌ Token refresh failed');
            // Clear token data instead of returning null
            token.accessToken = undefined;
            token.refreshToken = undefined;
            token.expiresAt = undefined;
          }
        } catch (error) {
          console.error('❌ Token refresh error:', error);
          // Clear token data instead of returning null
          token.accessToken = undefined;
          token.refreshToken = undefined;
          token.expiresAt = undefined;
        }
      }

      return token;
    },

    async session({ session, token }: { session: any; token: any }) {
      // Always fetch fresh user data from API
      if (token.accessToken) {
        const userProfile = await fetchUserProfile(token.accessToken as string);

        if (userProfile) {
          session.user = {
            id: userProfile.id,
            name: `${userProfile.firstName} ${userProfile.lastName}`,
            email: userProfile.email,
            firstName: userProfile.firstName,
            lastName: userProfile.lastName,
            phoneNumber: userProfile.phoneNumber,
            image: userProfile.image,
            oauthProvider: userProfile.oauthProvider,
            oauthId: userProfile.oauthId,
            isActive: userProfile.isActive,
            isBlocked: userProfile.isBlocked,
          };

          session.accessToken = token.accessToken as string;
          session.refreshToken = token.refreshToken as string;
          session.expiresAt = token.expiresAt as number;

          console.log('✅ Session updated with fresh profile data');
        } else {
          console.warn('❌ Failed to fetch profile, session may be invalid');
          // Return empty session instead of null
          return {
            ...session,
            user: null,
            accessToken: null,
            refreshToken: null,
            expiresAt: null,
          };
        }
      }

      return session;
    },
  },
};