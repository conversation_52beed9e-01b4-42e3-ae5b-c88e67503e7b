import NextAuth from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string;
      email: string;
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      image?: string;
    };
    accessToken: string;
    refreshToken?: string;
    expiresAt: number;
  }

  interface User {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    accessToken: string;
    refreshToken?: string;
    image?: string;
    oauthProvider?: string;
    oauthId?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    accessToken: string;
    refreshToken?: string;
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    email: string;
    image?: string;
    oauthProvider?: string;
    oauthId?: string;
    expiresAt: number;
  }
}
