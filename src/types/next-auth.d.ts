declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string;
      email: string;
      firstName: string;
      lastName: string;
      phoneNumber?: string;
      image?: string;
      oauthProvider?: string;
      oauthId?: string;
      isActive?: boolean;
      isBlocked?: boolean;
    };
    accessToken: string;
    refreshToken?: string;
    expiresAt: number;
  }

  interface User {
    id: string;
    name?: string;
    email: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    image?: string;
    oauthProvider?: string;
    oauthId?: string;
    isActive?: boolean;
    isBlocked?: boolean;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    fingerprint?: string;
  }
}
