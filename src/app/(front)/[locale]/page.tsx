'use client'
import ProductCategory from "@/components/product/ProductCategory"
import { ProductItem } from "@/types/product"
import { Box } from "@chakra-ui/react"
import dynamic from "next/dynamic"
import React from 'react'
import items from '@/datas/product.json'

const HeroSlider = dynamic(() => import('@/components/slider/HeroSlider'))

export default function page() {
  const sliderImages = [
    '/assets/images/banner-1.png',
    '/assets/images/banner-2.png',
  ]

  return (
    <Box>
      <Box bg="white" py={4}>
        <HeroSlider images={sliderImages} />
      </Box>
      <ProductCategory 
        items={items}
        title="Set For Stun: Star Wars Indonesia"
      />
      <ProductCategory 
        items={items}
        title="Shining Stars: Limited Edition Star Wars Posters"
      />
       <ProductCategory 
        items={items}
        title="Shining Stars: Limited Edition Star Wars Posters"
      />
    </Box>
  )
}