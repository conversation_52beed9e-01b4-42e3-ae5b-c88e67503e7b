'use client'
import {
    Box,
    <PERSON>lex,
    <PERSON>ing,
    Text,
    Badge,
    Button,
    Stack,
    HStack,
    VStack,
    Image,
    Icon,
    Grid,
    GridItem,
    Tag,
    Tabs,
    List,
    ListItem,
    Input,
    InputGroup,
    Breadcrumb,
} from '@chakra-ui/react';
import { Fa<PERSON><PERSON>, FaHistory } from 'react-icons/fa';
import products from '@/datas/product.json';
import { useParams } from 'next/navigation';
import ProductCardImage from '@/components/product/ProductCardImage';
import { ProductItem } from '@/types/product';
import { FaArrowUpRightFromSquare } from 'react-icons/fa6';
import ProductCategory from '@/components/product/ProductCategory';

const ProductDetailPage = () => {
    const { locale, category, slug } = useParams();
    const product: ProductItem = products.find((product) => product.slug === slug) as ProductItem;

    const handlePlaceBid = () => {

    };

    return (
        <Box>
            <Box mx="auto">
                <Flex
                    align="flex-start"
                    mx="auto"
                    gap={4}
                >
                    <Box
                        flex={1}
                        px={6}
                        alignSelf={'flex-start'}
                        height={{
                            base: '200px',
                            md: '650px',
                        }}>
                        <Breadcrumb.Root whiteSpace={"nowrap"}>
                            <Breadcrumb.List>
                                <Breadcrumb.Item>
                                    <Breadcrumb.Link href="/">Home</Breadcrumb.Link>
                                </Breadcrumb.Item>
                                <Breadcrumb.Separator />
                                <Breadcrumb.Item>
                                    <Breadcrumb.Link href="/auction">Auction</Breadcrumb.Link>
                                </Breadcrumb.Item>
                                <Breadcrumb.Separator />
                                <Breadcrumb.Item>
                                    <Breadcrumb.CurrentLink>
                                        <Text lineClamp={1} maxW={"600px"} fontSize="sm" fontWeight="bold" color="gray.600">
                                            {product.title}
                                        </Text>
                                    </Breadcrumb.CurrentLink>
                                </Breadcrumb.Item>
                            </Breadcrumb.List>
                        </Breadcrumb.Root>
                        <ProductCardImage
                            item={product}
                            boxSizeWatchList={6}
                        />
                    </Box>
                    <Box
                        flex={1}
                        alignSelf={'flex-start'}
                        bg="white"
                        px={20}
                        pt={16}
                        pb={12}
                        borderTopRightRadius="sm"
                        borderBottomRightRadius="sm"
                        position="sticky"
                        top={0}
                    >
                        <Box>
                            <Box mb={6}>
                                <Text fontSize="xl" fontWeight="bold" mb={1}>
                                    2013-14 Panini Flawless Patch Autographs #PA-KB Kobe Bryant Signed Patch Card (#22/25)
                                </Text>
                                <Text fontSize="md" color="gray.500" fontWeight={"semibold"}>
                                    BGS GEM MINT 9.5, Beckett 10
                                </Text>
                            </Box>

                            <HStack justifyContent={'space-between'} alignItems="start" gap={4} mb={4}>
                                <Box>
                                    <Text fontSize="sm" fontWeight="semibold" color="gray.500" mb={1}>
                                        Current Bid:
                                    </Text>
                                    <Heading size="6xl" color="black" fontWeight={'bold'}>
                                        $23,000
                                    </Heading>
                                </Box>
                                <Box>
                                    <Text fontSize="sm" fontWeight="semibold" color="gray.500" mb={1}>
                                        Bids
                                    </Text>
                                    <Text fontSize="4xl" fontWeight="bold">
                                        35
                                    </Text>
                                    <Text cursor="pointer" fontSize={12} onClick={() => { }} textDecoration="underline">
                                        <Icon as={FaHistory} boxSize={3} color="gray.600" me={2} />
                                        Show bid history
                                    </Text>
                                </Box>
                            </HStack>

                            <Flex justify="space-between" mb={6}>
                                <Box textAlign="left">
                                    <Text fontSize="sm" fontWeight="semibold" color="gray.500">
                                        Ends in
                                    </Text>
                                    <Flex align="center" justify="flex-end">
                                        <Icon as={FaClock} mr={1} />
                                        <Text color={"gray.600"} fontWeight="bold">10H 39M | Fri, 5/16/25, 9:00 AM</Text>
                                    </Flex>
                                </Box>
                            </Flex>

                            <VStack gap={4}>
                                <Button
                                    size="lg"
                                    w="full"
                                    borderRadius={30}
                                    onClick={handlePlaceBid}>
                                    Place Bid
                                </Button>
                            </VStack>
                            <Box as={'hr'} my={6} borderColor="gray.200" />
                            <Box>
                                <Heading as="h3" size="md" mb={3}>
                                    Description
                                </Heading>
                                <Text fontSize="sm" mb={4}>
                                    You are viewing one of the thousands of items in our 174th Weekly Sunday Auction, spanning all sports and genres, closing on the evening of May 18th 2025. Extended bidding starts at 7:00PM PT.
                                </Text>
                            </Box>
                            <Box as={'hr'} my={4} borderColor="gray.200" />
                            <Box>
                                <Heading as="h3" size="md" mb={3}>
                                    Sales History:
                                </Heading>
                                <Text fontSize="sm">
                                    View recent sales history and more about this listing on Goldin trusted partner site : <a href="https://www.cardladder.com/" target="_blank" rel="noopener noreferrer">cardladder.com</a>
                                </Text>
                                <HStack
                                    justifyContent={'space-between'}
                                    cursor={'pointer'}
                                    bg="gray.100"
                                    mt={4}
                                    py={3}
                                    px={4}
                                    borderRadius={8}
                                    onClick={handlePlaceBid}>
                                    <Text fontSize={'sm'} fontWeight="normal" color="gray.800">
                                        View Sales History on Card Ladder
                                    </Text>
                                    <Icon as={FaArrowUpRightFromSquare} boxSize={3.5} />
                                </HStack>
                            </Box>
                        </Box>
                    </Box>
                </Flex>
            </Box>

            <ProductCategory
                items={products}
                title="Related Items"
            />

             <ProductCategory
                items={products}
                title="Similar Items"
            />
        </Box>
    );
};

export default ProductDetailPage;