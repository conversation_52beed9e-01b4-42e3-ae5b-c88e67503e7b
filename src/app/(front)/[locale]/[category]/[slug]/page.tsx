'use client'
import { Box } from '@chakra-ui/react';
import { useParams } from 'next/navigation';
import products from '@/datas/product.json';
import { ProductItem } from '@/types/product';
import ProductCategory from '@/components/product/ProductCategory';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';

const ProductDetailPage = () => {
    const { slug } = useParams();
    const product: ProductItem = products.find((product) => product.slug === slug) as ProductItem;

    if (!product) {
        return <Box>Product not found</Box>;
    }

    return (
        <Box minH="100vh">
            <Box>
                <ProductDetailLayout product={product} />
            </Box>

            <Box>
                <ProductCategory
                    items={products}
                    title="Related Items"
                />

                <ProductCategory
                    items={products}
                    title="Similar Items"
                />
            </Box>
        </Box>
    );
};

export default ProductDetailPage;