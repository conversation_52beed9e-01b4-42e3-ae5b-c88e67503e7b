import { Session } from 'next-auth';

// Client-side session security utilities
export interface SecureSessionData {
  user: {
    id: string;
    name?: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber?: string;
    image?: string;
    oauthProvider?: string;
    oauthId?: string;
  };
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  isValid: boolean;
  isExpired: boolean;
}

// Validate JWT token structure (client-side only)
export const validateTokenStructure = (token: string): boolean => {
  if (!token) return false;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return false;
    
    // Try to decode header and payload
    const header = JSON.parse(atob(parts[0]));
    const payload = JSON.parse(atob(parts[1]));
    
    // Basic validation
    return !!(header.alg && payload.exp && payload.iat);
  } catch {
    return false;
  }
};

// Check if token is expired
export const isTokenExpired = (token: string): boolean => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return true;
    
    const payload = JSON.parse(atob(parts[1]));
    const now = Math.floor(Date.now() / 1000);
    
    return payload.exp < now;
  } catch {
    return true;
  }
};

// Get token expiry time
export const getTokenExpiry = (token: string): number => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return 0;
    
    const payload = JSON.parse(atob(parts[1]));
    return payload.exp || 0;
  } catch {
    return 0;
  }
};

// Get time until token expires (in seconds)
export const getTimeUntilExpiry = (token: string): number => {
  try {
    const expiry = getTokenExpiry(token);
    const now = Math.floor(Date.now() / 1000);
    return Math.max(0, expiry - now);
  } catch {
    return 0;
  }
};

// Validate session security
export const validateSessionSecurity = (session: Session | null): SecureSessionData | null => {
  if (!session || !session.user || !session.accessToken) {
    return null;
  }

  const isTokenValid = validateTokenStructure(session.accessToken);
  const isExpired = isTokenExpired(session.accessToken);

  return {
    user: {
      id: session.user.id,
      name: session.user.name,
      email: session.user.email,
      firstName: session.user.firstName,
      lastName: session.user.lastName,
      phoneNumber: session.user.phoneNumber,
      image: session.user.image,
      oauthProvider: session.user.oauthProvider,
      oauthId: session.user.oauthId,
    },
    accessToken: session.accessToken,
    refreshToken: session.refreshToken,
    expiresAt: session.expiresAt,
    isValid: isTokenValid && !isExpired,
    isExpired,
  };
};

// Get secure headers for API calls
export const getSecureHeaders = (accessToken: string): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  if (accessToken && validateTokenStructure(accessToken)) {
    headers['Authorization'] = `Bearer ${accessToken}`;
  }

  return headers;
};

// Sanitize session data for logging (remove sensitive info)
export const sanitizeSessionForLogging = (session: Session | null): any => {
  if (!session) return null;

  return {
    user: {
      id: session.user?.id ? '***' + session.user.id.slice(-4) : null,
      email: session.user?.email ? session.user.email.replace(/(.{2}).*(@.*)/, '$1***$2') : null,
      firstName: session.user?.firstName || null,
      hasImage: !!session.user?.image,
      provider: session.user?.oauthProvider || 'credentials',
    },
    hasAccessToken: !!session.accessToken,
    hasRefreshToken: !!session.refreshToken,
    expiresAt: session.expiresAt,
    isExpired: session.accessToken ? isTokenExpired(session.accessToken) : true,
  };
};

// Check if session needs refresh
export const shouldRefreshSession = (session: Session | null): boolean => {
  if (!session || !session.accessToken) return false;

  const timeUntilExpiry = getTimeUntilExpiry(session.accessToken);
  // Refresh if token expires in less than 5 minutes
  return timeUntilExpiry < 300;
};

// Get session status for UI
export const getSessionStatus = (session: Session | null): {
  status: 'authenticated' | 'unauthenticated' | 'expired' | 'invalid';
  message: string;
  needsRefresh: boolean;
} => {
  if (!session || !session.user) {
    return {
      status: 'unauthenticated',
      message: 'No active session',
      needsRefresh: false,
    };
  }

  if (!session.accessToken) {
    return {
      status: 'invalid',
      message: 'Invalid session - missing access token',
      needsRefresh: false,
    };
  }

  if (!validateTokenStructure(session.accessToken)) {
    return {
      status: 'invalid',
      message: 'Invalid token structure',
      needsRefresh: false,
    };
  }

  if (isTokenExpired(session.accessToken)) {
    return {
      status: 'expired',
      message: 'Session has expired',
      needsRefresh: true,
    };
  }

  const needsRefresh = shouldRefreshSession(session);
  
  return {
    status: 'authenticated',
    message: needsRefresh ? 'Session active (refresh recommended)' : 'Session active',
    needsRefresh,
  };
};

// Format time remaining until expiry
export const formatTimeUntilExpiry = (token: string): string => {
  const seconds = getTimeUntilExpiry(token);
  
  if (seconds <= 0) return 'Expired';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

// Security event types for client-side logging
export type SecurityEventType = 
  | 'session_created'
  | 'session_refreshed'
  | 'session_expired'
  | 'token_invalid'
  | 'unauthorized_access'
  | 'security_warning';

export interface SecurityEvent {
  type: SecurityEventType;
  timestamp: Date;
  details?: Record<string, any>;
  userAgent?: string;
  url?: string;
}

// Log security events (client-side)
export const logSecurityEvent = (event: SecurityEvent): void => {
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.log(`[SECURITY] ${event.type}:`, {
      timestamp: event.timestamp.toISOString(),
      details: event.details,
      userAgent: event.userAgent || navigator.userAgent,
      url: event.url || window.location.href,
    });
  }
  
  // In production, you might want to send to analytics service
  // Example: analytics.track('security_event', event);
};

// Create a secure session wrapper
export class SecureSession {
  private session: Session | null;
  
  constructor(session: Session | null) {
    this.session = session;
  }
  
  get isValid(): boolean {
    const status = getSessionStatus(this.session);
    return status.status === 'authenticated';
  }
  
  get isExpired(): boolean {
    const status = getSessionStatus(this.session);
    return status.status === 'expired';
  }
  
  get needsRefresh(): boolean {
    const status = getSessionStatus(this.session);
    return status.needsRefresh;
  }
  
  get user() {
    return this.session?.user || null;
  }
  
  get accessToken(): string | null {
    if (!this.isValid) return null;
    return this.session?.accessToken || null;
  }
  
  get refreshToken(): string | null {
    return this.session?.refreshToken || null;
  }
  
  getSecureHeaders(): Record<string, string> {
    const token = this.accessToken;
    return token ? getSecureHeaders(token) : { 'Content-Type': 'application/json' };
  }
  
  getStatus() {
    return getSessionStatus(this.session);
  }
  
  sanitizeForLogging() {
    return sanitizeSessionForLogging(this.session);
  }
}
