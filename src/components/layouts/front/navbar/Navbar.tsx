"use client"
import NavbarStyles from './navbar.module.css'
import {
    Box,
    Button,
    Container,
    Flex,
    HStack,
    Input,
    InputGroup,
    HoverCard,
    Portal,
    Stack,
    Text,
    createListCollection,
    Avatar,
    Icon,
    Skeleton,
} from '@chakra-ui/react'
import Image from 'next/image'
import Link from 'next/link'
import { Link as ChakraLink } from "@chakra-ui/react"
import React, { useEffect, useRef, useState } from 'react'
import { LuSearch } from 'react-icons/lu'
import { IoCartOutline, IoGlobeOutline, IoLogOut } from 'react-icons/io5'
import { ChakraSelect } from '@/components/ui/select/ChakraSelect'
import { useTranslations } from 'next-intl'
import { useParams, usePathname, useRouter } from 'next/navigation'
import { signOut, useSession } from 'next-auth/react'
import { FaAngleDown } from 'react-icons/fa'

type MenuListType = {
    label: string;
    href: string;
    children?: MenuListType[];
}

export const MenuList: MenuListType[] = [
    {
        label: "Auction",
        href: "/auction",
        children: [
            {
                label: "Live Auction",
                href: "/auction?type=live-auction"
            },
            {
                label: "Upcoming Auction",
                href: "/auction?type=upcoming-auction"
            },
            {
                label: "Past Auction",
                href: "/auction?type=past-auction"
            }
        ]
    },
    {
        label: "Buy Now",
        href: "/buy-now"
    },
    {
        label: "Trading Cards",
        href: "/auction",
        children: [
            {
                label: "Trading Card Sets",
                href: "/auction?type=trading-card-sets"
            },
            {
                label: "Trading Card Series",
                href: "/auction?type=trading-card-series"
            }
        ]
    },
    {
        label: "More",
        href: "/more"
    },
]

type MenuProfileListType = {
    label: string;
    href: string;
    onClick?: (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => void;
}

const MenuProfileList: MenuProfileListType[] = [
    {
        label: "Summary",
        href: "/summary"
    },
    {
        label: "Account",
        href: "/setting"
    },
    {
        label: "Selling",
        href: "/selling"
    },
    {
        label: "Buying",
        href: "/buying"
    },
    {
        label: "Bidding",
        href: "/bidding"
    },
    {
        label: "Watchlist",
        href: "/watchlist"
    },
    {
        label: "Logout",
        href: "#",
        onClick: (e) => {
            e.preventDefault();
            signOut()
        }
    }
]

type MenuDropdownType = {
    [key: string]: boolean;
}

export default function NavbarFront() {
    const dataSession = useSession()
    const session = dataSession.data;
    const router = useRouter();
    const pathname = usePathname();
    const params = useParams();
    const { locale } = params;
    const t = useTranslations();
    const selectLanguangeCurrencyRef = useRef<HTMLDivElement>(null);
    const profileCardRef = useRef<HTMLDivElement>(null);
    const [openCardLanguangeCurrency, setOpenCardLanguangeCurrency] = useState(false);
    const [openCardProfile, setOpenCardProfile] = useState(false);
    const [openMenuDropdown, setOpenMenuDropdown] = useState<MenuDropdownType>({});
    const [language, setLanguage] = useState<string[]>([locale as string]);
    const [currency, setCurrency] = useState<string[]>(["idr"]);

    const countries = createListCollection({
        items: [
            { value: "id", label: "Bahasa Indonesia" },
            { value: "en", label: "English" },
        ],
        itemToValue: (item) => item.value,
    })

    const currencies = createListCollection({
        items: [
            { value: "idr", label: "IDR - Indonesia Dupiah" },
            { value: "usd", label: "USD - US Dollar" },
        ],
        itemToValue: (item) => item.value,
    })

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectLanguangeCurrencyRef.current && !selectLanguangeCurrencyRef.current.contains(event.target as Node)) {
                setOpenCardLanguangeCurrency(false);
            }

            if (profileCardRef.current && !profileCardRef.current.contains(event.target as Node)) {
                setOpenCardProfile(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [selectLanguangeCurrencyRef]);

    const handleSubmitLanguageCurrency = () => {
        console.log("Language: ", language);
        console.log("Currency: ", currency);

        const parts = pathname.split('/');
        parts[1] = language[0];
        const newPath = parts.join('/');
        window.location.href = newPath;
    };

    return (
        <Box
            as="header"
            position="sticky"
            top="0"
            zIndex="1000"
            backdropFilter={"blur(100px)"}
            boxShadow="rgba(0, 0, 0, 0.04) 0px 3px 12px"
            backgroundColor="rgba(255, 255, 255, 0.8)"
        >
            <Container
                maxW="100%"
                py={3}>
                <Flex gap={6} align="center">
                    <Flex justify="space-between" align="center">
                        <Button variant="plain" display={{ base: "flex", lg: "none" }} p={2}>
                            <Image
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAAG1BMVEUjIyMvLy////+Xl5f4+Pjz8/Ourq6GhoalpaWPtzYJAAAAZklEQVRIx2NQwgBOGCIMSkSAQakolAjAYGxsbG5sXIyXYAACQQZGAXzEEAaCRACGNCIAgwsRYOimp1FFVFREVFIpJwIQl3yHd+a0IAKMJrpRRdSuOQmCAcgwAgQg3TNnBxFgeNecAHACSX1/gu7fAAAAAElFTkSuQmCC"
                                alt="hamburger"
                                width={20}
                                height={20}
                            />
                        </Button>
                        <Link
                            style={{
                                width: "100%",
                            }}
                            href='/'>
                            <Image
                                src="/logo.png"
                                alt="Logo"
                                width={100}
                                height={80}
                                objectFit="cover"
                                className={NavbarStyles.navbar_logo}
                            />
                        </Link>
                    </Flex>
                    <Flex
                        width="full"
                        gap={{
                            base: 2,
                            lg: 8
                        }}
                        align="center">
                        <Box
                            as={'form'}
                            onSubmit={(e) => {
                                e.preventDefault();
                                const searchValue = (e.target as HTMLFormElement).search.value;
                                console.log(searchValue);
                                if (searchValue) {
                                    router.push(`/${locale}/auction?keyword${searchValue}`);
                                }
                            }}
                            width={{
                                base: "100%",
                                lg: "260px",
                                xl: "340px",
                            }}
                        >
                            <InputGroup flex="1" startElement={<LuSearch size={22} />}>
                                <Input
                                    ps={12}
                                    placeholder={t('Navbar.placeholderSearch')}
                                    borderRadius="full"
                                    height="45px"
                                    bg="white"
                                    name='search'
                                    borderColor="gray.300"
                                    _hover={{ bg: 'gray.100' }}
                                    _focus={{ bg: 'white', borderColor: 'gray.200' }}
                                />
                            </InputGroup>
                        </Box>
                        <HStack
                            justify="start"
                            display={{
                                base: "none",
                                lg: "flex"
                            }}
                            gap={8}>
                            {
                                MenuList.map((item, index) =>
                                    (item.children?.length ?? 0) > 0 ? (
                                        <HoverCard.Root
                                            key={index}
                                            onOpenChange={(e) => {
                                                setOpenMenuDropdown((prev) => ({
                                                    ...prev,
                                                    [item.label]: e.open
                                                }));
                                            }}
                                            size="sm"
                                            open={openMenuDropdown[item.label] ?? false}>
                                            <HoverCard.Trigger asChild>
                                                <Box
                                                    textTransform="capitalize"
                                                    fontSize={"sm"}
                                                    color="gray.700"
                                                    fontWeight="semibold"
                                                    cursor={"pointer"}
                                                    onClick={() => {
                                                        setOpenMenuDropdown((prev) => ({
                                                            ...prev,
                                                            [item.label]: !prev[item.label]
                                                        }));
                                                    }}
                                                >
                                                    {item.label}

                                                    <Box
                                                        as="span"
                                                        ml={1}
                                                        fontSize="xs"
                                                        color="gray.500"
                                                        display="inline-block"
                                                        transform={openMenuDropdown[item.label] ? "rotate(180deg)" : "rotate(0deg)"}
                                                        transition="transform 0.2s ease-in-out"
                                                        className={NavbarStyles.dropdownIcon}>
                                                        <Icon
                                                            as={FaAngleDown}
                                                            boxSize={4}
                                                            color="gray.800"
                                                        />
                                                    </Box>
                                                </Box>
                                            </HoverCard.Trigger>
                                            <Portal>
                                                <HoverCard.Positioner placeContent={"center"}>
                                                    <HoverCard.Content p={2} minW={240} maxW={300}>
                                                        <Stack gap={0}>
                                                            {
                                                                item.children?.map((item, index) => (
                                                                    <ChakraLink
                                                                        key={index}
                                                                        as={Link}
                                                                        href={`/${locale}/${item.href}`}
                                                                        textStyle="sm"
                                                                        fontWeight="regular"
                                                                        color="gray.800"
                                                                        borderRadius={8}
                                                                        _hover={{
                                                                            textDecoration: "none",
                                                                            bg: "gray.100"
                                                                        }}
                                                                        px={3}
                                                                        py={2}
                                                                    >
                                                                        {item.label}
                                                                    </ChakraLink>
                                                                ))
                                                            }
                                                        </Stack>
                                                    </HoverCard.Content>
                                                </HoverCard.Positioner>
                                            </Portal>
                                        </HoverCard.Root>
                                    ) : (
                                        <ChakraLink
                                            key={index}
                                            textTransform="capitalize"
                                            fontSize={"sm"}
                                            color="gray.700"
                                            fontWeight="semibold"
                                            as={Link}
                                            href={item.href}
                                            _hover={{
                                                textDecoration: "none",
                                            }}
                                            _focus={{
                                                outline: "none",
                                            }}
                                        >
                                            {item.label}
                                        </ChakraLink>
                                    )
                                )
                            }
                        </HStack>
                        <HStack ms="auto" gap={2}>
                            <HoverCard.Root
                                onOpenChange={(e) => setOpenCardLanguangeCurrency(e.open)}
                                size="sm"
                                open={openCardLanguangeCurrency}>
                                <HoverCard.Trigger asChild>
                                    <Box
                                        display={{
                                            base: "none",
                                            md: "flex"
                                        }}
                                        alignItems="center"
                                        textStyle="sm"
                                        fontWeight="semibold"
                                        color="gray.700"
                                        borderRadius="full"
                                        px={3}
                                        py={2}
                                        _hover={{ bg: "gray.100" }}
                                        _active={{ bg: "gray.200" }}
                                        _focus={{ boxShadow: "outline" }}
                                        cursor={"pointer"}
                                        onClick={() => {
                                            setOpenCardLanguangeCurrency(!openCardLanguangeCurrency)
                                        }}
                                        whiteSpace="nowrap"
                                        fontSize={{
                                            base: 10,
                                            md: "sm"
                                        }}
                                    >
                                        <IoGlobeOutline size={20} />
                                        <Text
                                            fontWeight="semibold"
                                            color="gray.700"
                                            textTransform="uppercase"
                                            ml={2}>
                                            {language} - IDR
                                        </Text>
                                    </Box>
                                </HoverCard.Trigger>
                                <Portal>
                                    <HoverCard.Positioner>
                                        <HoverCard.Content ref={selectLanguangeCurrencyRef}>
                                            <HoverCard.Arrow />
                                            <Stack gap="4">
                                                <Text
                                                    fontSize="sm"
                                                    fontWeight="bold"
                                                    color="gray.700"
                                                >
                                                    {t('Navbar.languageCurrencyChangeText')}
                                                </Text>

                                                <ChakraSelect
                                                    label={t('language')}
                                                    placeholder={`Pilih ${t('language')}`}
                                                    collection={countries}
                                                    defaultValue={language}
                                                    portalRef={selectLanguangeCurrencyRef}
                                                    onValueChange={(e) => {
                                                        setLanguage(e.value)
                                                    }}
                                                />

                                                <ChakraSelect
                                                    label={t('currency')}
                                                    placeholder={`Pilih ${t('currency')}`}
                                                    collection={currencies}
                                                    defaultValue={currency}
                                                    portalRef={selectLanguangeCurrencyRef}
                                                    onValueChange={(e) => {
                                                        setCurrency(e.value)
                                                    }}
                                                />

                                                <Button
                                                    onClick={handleSubmitLanguageCurrency}
                                                    borderRadius={12}
                                                    px={6}
                                                    fontWeight={"bold"}
                                                    textTransform="uppercase"
                                                    size={"sm"}
                                                    variant="solid"
                                                    colorScheme="blue"
                                                    width="full"
                                                >
                                                    {t('save')}
                                                </Button>
                                            </Stack>
                                        </HoverCard.Content>
                                    </HoverCard.Positioner>
                                </Portal>
                            </HoverCard.Root>
                            {
                                dataSession.status == "loading" ? (
                                    <Skeleton
                                        width="180px"
                                        height="36px"
                                        borderRadius="lg" />
                                ) :
                                    session ? (
                                        <>
                                         <Box
                                                display={{
                                                    base: "none",
                                                    md: "flex"
                                                }}
                                                alignItems="center"
                                                textStyle="sm"
                                                fontWeight="semibold"
                                                color="gray.700"
                                                borderRadius="full"
                                                px={3}
                                                py={2}
                                                _hover={{ bg: "gray.100" }}
                                                _active={{ bg: "gray.200" }}
                                                _focus={{ boxShadow: "outline" }}
                                                cursor={"pointer"}
                                                whiteSpace="nowrap"
                                                fontSize={{
                                                    base: 10,
                                                    md: "sm"
                                                }}
                                            >
                                                <Icon
                                                    as={IoCartOutline}
                                                    boxSize={6}
                                                    color="gray.700"
                                                />
                                            </Box>
                                            <HoverCard.Root
                                                onOpenChange={(e) => setOpenCardProfile(e.open)}
                                                size="sm"
                                                open={openCardProfile}>
                                                <HoverCard.Trigger asChild>
                                                    <Box
                                                        display={{
                                                            base: "none",
                                                            md: "flex"
                                                        }}
                                                        alignItems="center"
                                                        textStyle="sm"
                                                        fontWeight="semibold"
                                                        color="gray.700"
                                                        borderRadius="full"
                                                        _hover={{ bg: "gray.100" }}
                                                        _active={{ bg: "gray.200" }}
                                                        _focus={{ boxShadow: "outline" }}
                                                        cursor={"pointer"}
                                                        onClick={() => {
                                                            setOpenCardProfile(!openCardProfile)
                                                        }}
                                                        whiteSpace="nowrap"
                                                        fontSize={{
                                                            base: 10,
                                                            md: "md"
                                                        }}
                                                    >
                                                        <Avatar.Root variant="outline" size="sm" borderWidth={2} borderColor="black">
                                                            <Avatar.Fallback name={session.user?.name ?? ""} />
                                                        </Avatar.Root>
                                                    </Box>
                                                </HoverCard.Trigger>
                                                <Portal>
                                                    <HoverCard.Positioner placeContent={"center"}>
                                                        <HoverCard.Content p={2} minW={240} maxW={300} ref={profileCardRef}>
                                                            <HStack gap={3} borderBottomWidth={1} borderColor="gray.200" px={3} pt={3} pb={4} alignItems="flex-start">
                                                                <Avatar.Root variant="outline" size="sm" borderWidth={2} borderColor="black">
                                                                    <Avatar.Fallback name={session.user?.name ?? ""} />
                                                                </Avatar.Root>
                                                                <Box>
                                                                    <Text
                                                                        as={"div"}
                                                                        fontWeight="bold"
                                                                        color="gray.800"
                                                                        fontSize="xs"
                                                                    >
                                                                        {session.user?.name ?? t('Navbar.unknownUser')}
                                                                    </Text>
                                                                    <Text
                                                                        as={"div"}
                                                                        fontWeight="regular"
                                                                        color="gray.500"
                                                                        fontSize="xs"
                                                                    >
                                                                        {session.user?.email ?? t('Navbar.unknownEmail')}
                                                                    </Text>
                                                                </Box>
                                                            </HStack>
                                                            <Stack gap={0} mt={2}>
                                                                {
                                                                    MenuProfileList.map((item, index) => (
                                                                        <ChakraLink
                                                                            key={index}
                                                                            as={Link}
                                                                            href={`/${locale}/account${item.href}`}
                                                                            textStyle="sm"
                                                                            fontWeight="regular"
                                                                            color="gray.800"
                                                                            borderRadius={8}
                                                                            _hover={{
                                                                                textDecoration: "none",
                                                                                bg: "gray.100"
                                                                            }}
                                                                            px={3}
                                                                            py={2}
                                                                            onClick={item.onClick}
                                                                        >
                                                                            {item.label}
                                                                            {
                                                                                item.label === "Logout" ? (
                                                                                    <Icon

                                                                                        as={IoLogOut}
                                                                                        ml="auto"
                                                                                        color="gray.500"
                                                                                        boxSize={4}
                                                                                    />
                                                                                ) : null
                                                                            }
                                                                        </ChakraLink>
                                                                    ))
                                                                }
                                                            </Stack>
                                                        </HoverCard.Content>
                                                    </HoverCard.Positioner>
                                                </Portal>
                                            </HoverCard.Root>
                                            <Button
                                                borderRadius={30}
                                                px={4}
                                                fontWeight={"bold"}
                                                textTransform="capitalize"
                                                display={{ base: "none", md: "block" }}
                                                size={"sm"}
                                                onClick={() => {
                                                    router.push(`/${locale}/selling`);
                                                }}
                                            >
                                                {t('Button.sellNow')}
                                            </Button>
                                        </>
                                    ) : (
                                        <>
                                            <Button
                                                borderRadius={30}
                                                px={4}
                                                fontWeight={"bold"}
                                                textTransform="capitalize"
                                                display={{ base: "none", md: "flex" }}
                                                size={"sm"}
                                                color={"gray.700"}
                                                borderColor={"gray.400"}
                                                variant="outline"
                                                onClick={() => {
                                                    router.push(`/${locale}/auth/login`);
                                                }}
                                            >
                                                {t('Button.login')}
                                            </Button>
                                            <Button
                                                borderRadius={30}
                                                px={4}
                                                fontWeight={"bold"}
                                                textTransform="capitalize"
                                                display={{ base: "none", md: "block" }}
                                                size={"sm"}
                                                onClick={() => {
                                                    router.push(`/${locale}/auth/register`);
                                                }}
                                            >
                                                {t('Button.sellNow')}
                                            </Button>
                                        </>
                                    )

                            }

                        </HStack>
                    </Flex>
                </Flex>
            </Container>
        </Box>
    )
}
