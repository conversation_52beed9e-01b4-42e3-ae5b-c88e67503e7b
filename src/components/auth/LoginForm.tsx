'use client'
import React, { useState } from 'react';
import {
    Box,
    Button,
    VStack,
    HStack,
    Text,
    Divider,
    useToast,
    FormControl,
    FormLabel,
    Input,
    FormErrorMessage,
    Checkbox,
    Link as ChakraLink,
    Spinner
} from '@chakra-ui/react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/hooks/useAuth';
import GoogleSignInButton from './GoogleSignInButton';

const loginSchema = z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(1, 'Password is required'),
    rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
    onSuccess?: () => void;
    redirectTo?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ 
    onSuccess,
    redirectTo = '/' 
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const { signInWithCredentials } = useAuth();
    const toast = useToast();

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        reset
    } = useForm<LoginFormData>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
            rememberMe: false,
        },
    });

    const onSubmit = async (data: LoginFormData) => {
        try {
            setIsLoading(true);
            const result = await signInWithCredentials(data.email, data.password);
            
            if (result?.ok) {
                toast({
                    title: 'Welcome back!',
                    description: 'You have successfully signed in',
                    status: 'success',
                    duration: 3000,
                    isClosable: true,
                });
                
                reset();
                onSuccess?.();
                
                // Redirect after successful login
                if (redirectTo) {
                    window.location.href = redirectTo;
                }
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to sign in';
            
            toast({
                title: 'Sign In Failed',
                description: errorMessage,
                status: 'error',
                duration: 5000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleGoogleSuccess = () => {
        toast({
            title: 'Welcome!',
            description: 'You have successfully signed in with Google',
            status: 'success',
            duration: 3000,
            isClosable: true,
        });
        
        onSuccess?.();
        
        if (redirectTo) {
            window.location.href = redirectTo;
        }
    };

    return (
        <Box
            maxW="400px"
            mx="auto"
            p={8}
            borderRadius="lg"
            boxShadow="lg"
            bg="white"
        >
            <VStack spacing={6} align="stretch">
                <Box textAlign="center">
                    <Text fontSize="2xl" fontWeight="bold" color="gray.800" mb={2}>
                        Welcome Back
                    </Text>
                    <Text color="gray.600">
                        Sign in to your account to continue
                    </Text>
                </Box>

                {/* Google Sign In */}
                <GoogleSignInButton
                    onSuccess={handleGoogleSuccess}
                    isDisabled={isLoading || isSubmitting}
                />

                {/* Divider */}
                <HStack>
                    <Divider />
                    <Text fontSize="sm" color="gray.500" whiteSpace="nowrap">
                        or continue with email
                    </Text>
                    <Divider />
                </HStack>

                {/* Email/Password Form */}
                <form onSubmit={handleSubmit(onSubmit)}>
                    <VStack spacing={4}>
                        <FormControl isInvalid={!!errors.email}>
                            <FormLabel>Email Address</FormLabel>
                            <Input
                                type="email"
                                placeholder="Enter your email"
                                {...register('email')}
                                isDisabled={isLoading || isSubmitting}
                            />
                            <FormErrorMessage>
                                {errors.email?.message}
                            </FormErrorMessage>
                        </FormControl>

                        <FormControl isInvalid={!!errors.password}>
                            <FormLabel>Password</FormLabel>
                            <Input
                                type="password"
                                placeholder="Enter your password"
                                {...register('password')}
                                isDisabled={isLoading || isSubmitting}
                            />
                            <FormErrorMessage>
                                {errors.password?.message}
                            </FormErrorMessage>
                        </FormControl>

                        <HStack justify="space-between" w="100%">
                            <Checkbox
                                {...register('rememberMe')}
                                isDisabled={isLoading || isSubmitting}
                            >
                                <Text fontSize="sm">Remember me</Text>
                            </Checkbox>
                            <ChakraLink
                                as={Link}
                                href="/auth/forgot-password"
                                fontSize="sm"
                                color="blue.500"
                                _hover={{ color: 'blue.600' }}
                            >
                                Forgot password?
                            </ChakraLink>
                        </HStack>

                        <Button
                            type="submit"
                            colorScheme="blue"
                            size="lg"
                            width="100%"
                            isLoading={isLoading || isSubmitting}
                            loadingText="Signing in..."
                            spinner={<Spinner size="sm" />}
                        >
                            Sign In
                        </Button>
                    </VStack>
                </form>

                {/* Sign Up Link */}
                <Box textAlign="center">
                    <Text fontSize="sm" color="gray.600">
                        Don't have an account?{' '}
                        <ChakraLink
                            as={Link}
                            href="/auth/register"
                            color="blue.500"
                            fontWeight="medium"
                            _hover={{ color: 'blue.600' }}
                        >
                            Sign up here
                        </ChakraLink>
                    </Text>
                </Box>
            </VStack>
        </Box>
    );
};

export default LoginForm;
