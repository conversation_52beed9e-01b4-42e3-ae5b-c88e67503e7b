'use client'
import React, { useState, useEffect } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Badge,
    useToast,
    Spinner,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Divider,
    Flex,
    Icon
} from '@chakra-ui/react';
import { FaSync, FaUser, FaEnvelope, FaPhone, FaImage, FaClock } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';

const RealTimeProfile: React.FC = () => {
    const { 
        user, 
        isLoading, 
        isAuthenticated, 
        refreshProfile,
        sessionStatus,
        secureSession 
    } = useAuth();
    
    const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
    const [refreshCount, setRefreshCount] = useState(0);
    const toast = useToast();

    // Update last refresh time when user data changes
    useEffect(() => {
        if (user) {
            setLastRefresh(new Date());
            setRefreshCount(prev => prev + 1);
        }
    }, [user?.firstName, user?.lastName, user?.email, user?.phoneNumber, user?.image]);

    const handleManualRefresh = async () => {
        try {
            await refreshProfile();
            toast({
                title: 'Profile Refreshed',
                description: 'Your profile has been updated with the latest data from the server',
                status: 'success',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            toast({
                title: 'Refresh Failed',
                description: 'Failed to refresh profile data',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        }
    };

    if (!isAuthenticated) {
        return (
            <Alert status="warning">
                <AlertIcon />
                <AlertTitle>Not Authenticated</AlertTitle>
                <AlertDescription>Please log in to view your profile.</AlertDescription>
            </Alert>
        );
    }

    return (
        <Box maxW="600px" mx="auto" p={6}>
            <VStack spacing={6}>
                <Box textAlign="center">
                    <Heading size="lg" mb={2}>
                        🔄 Real-Time Profile
                    </Heading>
                    <Text color="gray.600">
                        Your profile data is automatically synced with the server
                    </Text>
                </Box>

                {/* Refresh Status */}
                <Card w="100%">
                    <CardHeader>
                        <Flex justify="space-between" align="center">
                            <Heading size="md">Sync Status</Heading>
                            <Button
                                leftIcon={<Icon as={FaSync} />}
                                colorScheme="blue"
                                size="sm"
                                onClick={handleManualRefresh}
                                isLoading={isLoading}
                                loadingText="Syncing..."
                            >
                                Manual Sync
                            </Button>
                        </Flex>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Text fontWeight="bold">Session Status:</Text>
                                <Badge colorScheme={
                                    sessionStatus.status === 'authenticated' ? 'green' :
                                    sessionStatus.status === 'expired' ? 'red' : 'orange'
                                }>
                                    {sessionStatus.status.toUpperCase()}
                                </Badge>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Sync Mode:</Text>
                                <Badge colorScheme="blue">
                                    On Page Load/Navigation
                                </Badge>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Refresh Count:</Text>
                                <Text>{refreshCount}</Text>
                            </HStack>
                            {lastRefresh && (
                                <HStack>
                                    <Icon as={FaClock} color="gray.500" />
                                    <Text fontSize="sm" color="gray.600">
                                        Last updated: {lastRefresh.toLocaleTimeString()}
                                    </Text>
                                </HStack>
                            )}
                            {isLoading && (
                                <HStack>
                                    <Spinner size="sm" />
                                    <Text fontSize="sm" color="blue.500">
                                        Syncing with server...
                                    </Text>
                                </HStack>
                            )}
                        </VStack>
                    </CardBody>
                </Card>

                {/* Profile Data */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Profile Information</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={4}>
                            <HStack>
                                <Icon as={FaUser} color="blue.500" />
                                <Text fontWeight="bold">Name:</Text>
                                <Text>{user?.firstName} {user?.lastName}</Text>
                            </HStack>
                            
                            <Divider />
                            
                            <HStack>
                                <Icon as={FaEnvelope} color="green.500" />
                                <Text fontWeight="bold">Email:</Text>
                                <Text>{user?.email}</Text>
                            </HStack>
                            
                            <Divider />
                            
                            <HStack>
                                <Icon as={FaPhone} color="orange.500" />
                                <Text fontWeight="bold">Phone:</Text>
                                <Text>{user?.phoneNumber || 'Not provided'}</Text>
                            </HStack>
                            
                            <Divider />
                            
                            <HStack>
                                <Icon as={FaImage} color="purple.500" />
                                <Text fontWeight="bold">Profile Image:</Text>
                                <Text>{user?.image ? 'Available' : 'Not set'}</Text>
                            </HStack>
                            
                            {user?.oauthProvider && (
                                <>
                                    <Divider />
                                    <HStack>
                                        <Text fontWeight="bold">Login Provider:</Text>
                                        <Badge colorScheme="blue">
                                            {user.oauthProvider}
                                        </Badge>
                                    </HStack>
                                </>
                            )}
                        </VStack>
                    </CardBody>
                </Card>

                {/* Real-time Features */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">🚀 Real-time Features</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={2}>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Profile sync on page load/refresh</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Profile sync on navigation</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Manual refresh on demand</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Sync when tab becomes visible</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Encrypted session data storage</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Automatic logout on token expiry</Text>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Instructions */}
                <Alert status="info">
                    <AlertIcon />
                    <Box>
                        <AlertTitle>How it works:</AlertTitle>
                        <AlertDescription>
                            Your profile data is automatically fetched from the server when you load/refresh the page
                            or navigate between pages. Any changes made to your profile will be reflected
                            without needing to logout and login again.
                        </AlertDescription>
                    </Box>
                </Alert>

                {/* Debug Info (Development only) */}
                {process.env.NODE_ENV === 'development' && (
                    <Card w="100%" borderColor="yellow.200">
                        <CardHeader>
                            <Heading size="sm" color="yellow.600">🔧 Debug Info</Heading>
                        </CardHeader>
                        <CardBody>
                            <VStack align="start" spacing={2}>
                                <Text fontSize="xs">
                                    <strong>Session Valid:</strong> {secureSession.isValid ? 'Yes' : 'No'}
                                </Text>
                                <Text fontSize="xs">
                                    <strong>Token Valid:</strong> {secureSession.accessToken ? 'Yes' : 'No'}
                                </Text>
                                <Text fontSize="xs">
                                    <strong>Needs Refresh:</strong> {sessionStatus.needsRefresh ? 'Yes' : 'No'}
                                </Text>
                                <Text fontSize="xs">
                                    <strong>Status Message:</strong> {sessionStatus.message}
                                </Text>
                            </VStack>
                        </CardBody>
                    </Card>
                )}
            </VStack>
        </Box>
    );
};

export default RealTimeProfile;
