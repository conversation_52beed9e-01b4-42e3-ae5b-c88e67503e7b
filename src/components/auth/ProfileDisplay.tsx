'use client'
import React from 'react';
import {
    <PERSON>,
    VStack,
    HStack,
    Text,
    <PERSON>ton,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Badge,
    Avatar,
    useToast,
    Spinner,
    Icon,
    Flex
} from '@chakra-ui/react';
import { FaSync, FaUser, FaEnvelope, FaPhone, FaClock } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';

interface ProfileDisplayProps {
    showRefreshButton?: boolean;
    showSyncStatus?: boolean;
    compact?: boolean;
}

const ProfileDisplay: React.FC<ProfileDisplayProps> = ({
    showRefreshButton = true,
    showSyncStatus = true,
    compact = false
}) => {
    const { 
        user, 
        isLoading, 
        isAuthenticated, 
        refreshProfile,
        sessionStatus,
        isTokenValid
    } = useAuth();
    
    const toast = useToast();

    const handleRefresh = async () => {
        try {
            await refreshProfile();
            toast({
                title: 'Profile Refreshed',
                description: 'Your profile has been updated with the latest data',
                status: 'success',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            toast({
                title: 'Refresh Failed',
                description: 'Failed to refresh profile data',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        }
    };

    if (!isAuthenticated) {
        return (
            <Card>
                <CardBody>
                    <Text color="gray.500" textAlign="center">
                        Please log in to view your profile
                    </Text>
                </CardBody>
            </Card>
        );
    }

    if (compact) {
        return (
            <Card size="sm">
                <CardBody>
                    <HStack spacing={3}>
                        <Avatar 
                            size="sm" 
                            name={`${user?.firstName} ${user?.lastName}`}
                            src={user?.image}
                        />
                        <VStack align="start" spacing={0} flex={1}>
                            <Text fontWeight="bold" fontSize="sm">
                                {user?.firstName} {user?.lastName}
                            </Text>
                            <Text fontSize="xs" color="gray.600">
                                {user?.email}
                            </Text>
                        </VStack>
                        {showRefreshButton && (
                            <Button
                                size="xs"
                                variant="ghost"
                                onClick={handleRefresh}
                                isLoading={isLoading}
                            >
                                <Icon as={FaSync} />
                            </Button>
                        )}
                    </HStack>
                </CardBody>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <Flex justify="space-between" align="center">
                    <Heading size="md">Profile Information</Heading>
                    {showRefreshButton && (
                        <Button
                            leftIcon={<Icon as={FaSync} />}
                            colorScheme="blue"
                            size="sm"
                            onClick={handleRefresh}
                            isLoading={isLoading}
                            loadingText="Refreshing..."
                        >
                            Refresh
                        </Button>
                    )}
                </Flex>
            </CardHeader>
            <CardBody>
                <VStack spacing={4}>
                    {/* Profile Picture and Name */}
                    <VStack spacing={3}>
                        <Avatar 
                            size="lg" 
                            name={`${user?.firstName} ${user?.lastName}`}
                            src={user?.image}
                        />
                        <VStack spacing={1}>
                            <Text fontSize="xl" fontWeight="bold">
                                {user?.firstName} {user?.lastName}
                            </Text>
                            {user?.oauthProvider && (
                                <Badge colorScheme="blue" size="sm">
                                    {user.oauthProvider}
                                </Badge>
                            )}
                        </VStack>
                    </VStack>

                    {/* Profile Details */}
                    <VStack align="start" spacing={3} w="100%">
                        <HStack w="100%">
                            <Icon as={FaEnvelope} color="blue.500" />
                            <Text fontWeight="bold" minW="80px">Email:</Text>
                            <Text flex={1}>{user?.email}</Text>
                        </HStack>

                        <HStack w="100%">
                            <Icon as={FaPhone} color="green.500" />
                            <Text fontWeight="bold" minW="80px">Phone:</Text>
                            <Text flex={1}>{user?.phoneNumber || 'Not provided'}</Text>
                        </HStack>

                        <HStack w="100%">
                            <Icon as={FaUser} color="purple.500" />
                            <Text fontWeight="bold" minW="80px">ID:</Text>
                            <Text flex={1} fontSize="sm" fontFamily="mono">
                                {user?.id}
                            </Text>
                        </HStack>
                    </VStack>

                    {/* Sync Status */}
                    {showSyncStatus && (
                        <Box w="100%" pt={4} borderTop="1px" borderColor="gray.200">
                            <VStack spacing={2}>
                                <HStack w="100%" justify="space-between">
                                    <Text fontSize="sm" fontWeight="bold">Sync Status:</Text>
                                    <Badge colorScheme={
                                        sessionStatus.status === 'authenticated' ? 'green' :
                                        sessionStatus.status === 'expired' ? 'red' : 'orange'
                                    }>
                                        {sessionStatus.status.toUpperCase()}
                                    </Badge>
                                </HStack>

                                <HStack w="100%" justify="space-between">
                                    <Text fontSize="sm" fontWeight="bold">Token Valid:</Text>
                                    <Badge colorScheme={isTokenValid ? 'green' : 'red'}>
                                        {isTokenValid ? 'Valid' : 'Invalid'}
                                    </Badge>
                                </HStack>

                                <HStack w="100%" justify="space-between">
                                    <Text fontSize="sm" fontWeight="bold">Auto-Sync:</Text>
                                    <Badge colorScheme="blue">
                                        Every 30s
                                    </Badge>
                                </HStack>

                                {isLoading && (
                                    <HStack w="100%" justify="center" pt={2}>
                                        <Spinner size="sm" />
                                        <Text fontSize="sm" color="blue.500">
                                            Syncing with server...
                                        </Text>
                                    </HStack>
                                )}

                                <Text fontSize="xs" color="gray.500" textAlign="center">
                                    {sessionStatus.message}
                                </Text>
                            </VStack>
                        </Box>
                    )}
                </VStack>
            </CardBody>
        </Card>
    );
};

export default ProfileDisplay;
