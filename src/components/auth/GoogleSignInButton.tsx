'use client'
import React, { useState } from 'react';
import { Button, Icon, useToast } from '@chakra-ui/react';
import { FaGoogle } from 'react-icons/fa';
import { signIn } from 'next-auth/react';

interface GoogleSignInButtonProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'solid' | 'outline' | 'ghost';
  colorScheme?: string;
  width?: string | number;
  callbackUrl?: string;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  size = 'md',
  variant = 'outline',
  colorScheme = 'red',
  width = '100%',
  callbackUrl = '/dashboard'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      const result = await signIn('google', {
        callbackUrl,
        redirect: false,
      });

      if (result?.error) {
        toast({
          title: 'Sign In Failed',
          description: result.error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else if (result?.ok) {
        toast({
          title: 'Sign In Successful',
          description: 'Welcome! Redirecting...',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        
        // Redirect manually if needed
        if (callbackUrl) {
          window.location.href = callbackUrl;
        }
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      toast({
        title: 'Sign In Error',
        description: 'An unexpected error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      leftIcon={<Icon as={FaGoogle} />}
      colorScheme={colorScheme}
      variant={variant}
      size={size}
      width={width}
      onClick={handleGoogleSignIn}
      isLoading={isLoading}
      loadingText="Signing in..."
    >
      Continue with Google
    </Button>
  );
};

export default GoogleSignInButton;
