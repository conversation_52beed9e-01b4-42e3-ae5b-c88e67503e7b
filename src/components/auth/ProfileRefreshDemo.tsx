'use client'
import React, { useState, useEffect } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Badge,
    useToast,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Divider,
    Icon,
    Flex
} from '@chakra-ui/react';
import { FaSync, Fa<PERSON>ser, FaEnvelope, FaPhone, FaEye, FaMousePointer } from 'react-icons/fa';
import { useAuth } from '@/hooks/useAuth';
import { useSimpleProfileRefresh } from '@/hooks/useProfileRefresh';

const ProfileRefreshDemo: React.FC = () => {
    const { 
        user, 
        isLoading, 
        isAuthenticated, 
        sessionStatus 
    } = useAuth();
    
    const simpleRefresh = useSimpleProfileRefresh();
    const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
    const [refreshCount, setRefreshCount] = useState(0);
    const [isManualRefreshing, setIsManualRefreshing] = useState(false);
    const toast = useToast();

    // Track when user data changes (indicates a refresh happened)
    useEffect(() => {
        if (user) {
            setLastRefresh(new Date());
            setRefreshCount(prev => prev + 1);
        }
    }, [user?.firstName, user?.lastName, user?.email, user?.phoneNumber]);

    const handleManualRefresh = async () => {
        setIsManualRefreshing(true);
        try {
            const success = await simpleRefresh();
            if (success) {
                toast({
                    title: 'Profile Refreshed',
                    description: 'Your profile has been updated with the latest data',
                    status: 'success',
                    duration: 3000,
                    isClosable: true,
                });
            } else {
                toast({
                    title: 'Refresh Failed',
                    description: 'Failed to refresh profile data',
                    status: 'error',
                    duration: 3000,
                    isClosable: true,
                });
            }
        } catch (error) {
            toast({
                title: 'Error',
                description: 'An error occurred while refreshing',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        } finally {
            setIsManualRefreshing(false);
        }
    };

    if (!isAuthenticated) {
        return (
            <Alert status="warning">
                <AlertIcon />
                <AlertTitle>Not Authenticated</AlertTitle>
                <AlertDescription>Please log in to view profile refresh demo.</AlertDescription>
            </Alert>
        );
    }

    return (
        <Box maxW="700px" mx="auto" p={6}>
            <VStack spacing={6}>
                <Box textAlign="center">
                    <Heading size="lg" mb={2}>
                        🔄 Profile Refresh Demo
                    </Heading>
                    <Text color="gray.600">
                        Profile data refreshes on page load, navigation, and manual trigger
                    </Text>
                </Box>

                {/* Current Profile */}
                <Card w="100%">
                    <CardHeader>
                        <Flex justify="space-between" align="center">
                            <Heading size="md">Current Profile</Heading>
                            <Button
                                leftIcon={<Icon as={FaSync} />}
                                colorScheme="blue"
                                size="sm"
                                onClick={handleManualRefresh}
                                isLoading={isManualRefreshing || isLoading}
                                loadingText="Refreshing..."
                            >
                                Manual Refresh
                            </Button>
                        </Flex>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Icon as={FaUser} color="blue.500" />
                                <Text fontWeight="bold">Name:</Text>
                                <Text>{user?.firstName} {user?.lastName}</Text>
                            </HStack>
                            
                            <HStack>
                                <Icon as={FaEnvelope} color="green.500" />
                                <Text fontWeight="bold">Email:</Text>
                                <Text>{user?.email}</Text>
                            </HStack>
                            
                            <HStack>
                                <Icon as={FaPhone} color="orange.500" />
                                <Text fontWeight="bold">Phone:</Text>
                                <Text>{user?.phoneNumber || 'Not provided'}</Text>
                            </HStack>

                            {user?.oauthProvider && (
                                <HStack>
                                    <Text fontWeight="bold">Provider:</Text>
                                    <Badge colorScheme="blue">
                                        {user.oauthProvider}
                                    </Badge>
                                </HStack>
                            )}
                        </VStack>
                    </CardBody>
                </Card>

                {/* Refresh Status */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Refresh Status</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Text fontWeight="bold">Session Status:</Text>
                                <Badge colorScheme={
                                    sessionStatus.status === 'authenticated' ? 'green' :
                                    sessionStatus.status === 'expired' ? 'red' : 'orange'
                                }>
                                    {sessionStatus.status.toUpperCase()}
                                </Badge>
                            </HStack>

                            <HStack>
                                <Text fontWeight="bold">Refresh Count:</Text>
                                <Text>{refreshCount}</Text>
                            </HStack>

                            {lastRefresh && (
                                <HStack>
                                    <Text fontWeight="bold">Last Refresh:</Text>
                                    <Text>{lastRefresh.toLocaleTimeString()}</Text>
                                </HStack>
                            )}

                            <HStack>
                                <Text fontWeight="bold">Loading:</Text>
                                <Badge colorScheme={isLoading ? 'yellow' : 'green'}>
                                    {isLoading ? 'Yes' : 'No'}
                                </Badge>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Refresh Triggers */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">🚀 Automatic Refresh Triggers</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Icon as={FaMousePointer} color="blue.500" />
                                <Text fontSize="sm"><strong>Page Load/Refresh:</strong> Profile syncs when page loads</Text>
                            </HStack>
                            <HStack>
                                <Icon as={FaMousePointer} color="green.500" />
                                <Text fontSize="sm"><strong>Navigation:</strong> Profile syncs when navigating between pages</Text>
                            </HStack>
                            <HStack>
                                <Icon as={FaEye} color="purple.500" />
                                <Text fontSize="sm"><strong>Tab Focus:</strong> Profile syncs when returning to tab</Text>
                            </HStack>
                            <HStack>
                                <Icon as={FaSync} color="orange.500" />
                                <Text fontSize="sm"><strong>Manual Trigger:</strong> Click refresh button anytime</Text>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Test Instructions */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">🧪 How to Test</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <Text fontSize="sm">
                                <strong>1. Page Refresh:</strong> Press F5 or Ctrl+R to reload the page
                            </Text>
                            <Text fontSize="sm">
                                <strong>2. Navigation:</strong> Navigate to another page and come back
                            </Text>
                            <Text fontSize="sm">
                                <strong>3. Tab Switch:</strong> Switch to another tab and return
                            </Text>
                            <Text fontSize="sm">
                                <strong>4. Manual Refresh:</strong> Click the "Manual Refresh" button
                            </Text>
                            <Divider />
                            <Text fontSize="sm" color="blue.600">
                                <strong>Note:</strong> Watch the "Refresh Count" and "Last Refresh" to see when profile data updates
                            </Text>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Benefits */}
                <Alert status="success">
                    <AlertIcon />
                    <Box>
                        <AlertTitle>No More Logout/Login Required!</AlertTitle>
                        <AlertDescription>
                            Profile data automatically refreshes when you load pages or navigate. 
                            Any changes to your profile will be visible immediately without needing to logout and login again.
                        </AlertDescription>
                    </Box>
                </Alert>

                {/* Technical Info */}
                {process.env.NODE_ENV === 'development' && (
                    <Card w="100%" borderColor="yellow.200">
                        <CardHeader>
                            <Heading size="sm" color="yellow.600">🔧 Technical Info</Heading>
                        </CardHeader>
                        <CardBody>
                            <VStack align="start" spacing={2}>
                                <Text fontSize="xs">
                                    <strong>Sync Mode:</strong> On-demand (no intervals)
                                </Text>
                                <Text fontSize="xs">
                                    <strong>API Endpoint:</strong> GET /auth/profile
                                </Text>
                                <Text fontSize="xs">
                                    <strong>Session Update:</strong> NextAuth session.update()
                                </Text>
                                <Text fontSize="xs">
                                    <strong>Triggers:</strong> Page load, navigation, tab focus, manual
                                </Text>
                            </VStack>
                        </CardBody>
                    </Card>
                )}
            </VStack>
        </Box>
    );
};

export default ProfileRefreshDemo;
