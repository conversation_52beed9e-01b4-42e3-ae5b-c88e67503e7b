'use client'
import React, { useState } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    Input,
    FormControl,
    FormLabel,
    FormErrorMessage,
    Card,
    CardBody,
    CardHeader,
    Heading,
    useToast,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Divider,
    Icon,
    InputGroup,
    InputRightElement,
    IconButton,
} from '@chakra-ui/react';
import { FaGoogle, FaEye, FaEyeSlash, FaLock, FaEnvelope } from 'react-icons/fa';
import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface LoginFormData {
    emailPhoneNumber: string;
    password: string;
}

const SecureLoginForm: React.FC = () => {
    const [formData, setFormData] = useState<LoginFormData>({
        emailPhoneNumber: '',
        password: '',
    });
    const [errors, setErrors] = useState<Partial<LoginFormData>>({});
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const toast = useToast();
    const router = useRouter();

    // Input validation
    const validateForm = (): boolean => {
        const newErrors: Partial<LoginFormData> = {};

        if (!formData.emailPhoneNumber.trim()) {
            newErrors.emailPhoneNumber = 'Email or phone number is required';
        } else if (formData.emailPhoneNumber.length > 254) {
            newErrors.emailPhoneNumber = 'Email too long';
        } else {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            const cleanPhone = formData.emailPhoneNumber.replace(/[\s\-\(\)]/g, '');
            if (!emailRegex.test(formData.emailPhoneNumber) && !phoneRegex.test(cleanPhone)) {
                newErrors.emailPhoneNumber = 'Invalid email or phone number format';
            }
        }

        if (!formData.password) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 8) {
            newErrors.password = 'Password must be at least 8 characters';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field: keyof LoginFormData, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    const handleCredentialsLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);
        try {
            const result = await signIn('credentials', {
                emailPhoneNumber: formData.emailPhoneNumber.trim(),
                password: formData.password,
                redirect: false,
            });

            if (result?.error) {
                toast({
                    title: 'Login Failed',
                    description: result.error,
                    status: 'error',
                    duration: 5000,
                    isClosable: true,
                });
            } else if (result?.ok) {
                // Get fresh session
                const session = await getSession();
                
                if (session) {
                    toast({
                        title: 'Login Successful',
                        description: `Welcome back, ${session.user?.firstName}!`,
                        status: 'success',
                        duration: 3000,
                        isClosable: true,
                    });
                    
                    router.push('/dashboard');
                } else {
                    toast({
                        title: 'Session Error',
                        description: 'Please try logging in again',
                        status: 'warning',
                        duration: 3000,
                        isClosable: true,
                    });
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            toast({
                title: 'Login Error',
                description: 'An unexpected error occurred',
                status: 'error',
                duration: 5000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleGoogleLogin = async () => {
        setIsLoading(true);
        try {
            const result = await signIn('google', {
                redirect: false,
                callbackUrl: '/dashboard',
            });

            if (result?.error) {
                toast({
                    title: 'Google Login Failed',
                    description: result.error,
                    status: 'error',
                    duration: 5000,
                    isClosable: true,
                });
            } else if (result?.ok) {
                // Get fresh session
                const session = await getSession();
                
                if (session) {
                    toast({
                        title: 'Google Login Successful',
                        description: `Welcome, ${session.user?.firstName}!`,
                        status: 'success',
                        duration: 3000,
                        isClosable: true,
                    });
                    
                    router.push('/dashboard');
                }
            }
        } catch (error) {
            console.error('Google login error:', error);
            toast({
                title: 'Google Login Error',
                description: 'Failed to authenticate with Google',
                status: 'error',
                duration: 5000,
                isClosable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Box maxW="400px" mx="auto" p={6}>
            <Card>
                <CardHeader>
                    <VStack spacing={2}>
                        <Icon as={FaLock} boxSize={8} color="blue.500" />
                        <Heading size="lg" textAlign="center">
                            Secure Login
                        </Heading>
                        <Text color="gray.600" textAlign="center" fontSize="sm">
                            Sign in to your account with enhanced security
                        </Text>
                    </VStack>
                </CardHeader>
                <CardBody>
                    <VStack spacing={6}>
                        {/* Google Login */}
                        <Button
                            leftIcon={<Icon as={FaGoogle} />}
                            colorScheme="red"
                            variant="outline"
                            size="lg"
                            width="100%"
                            onClick={handleGoogleLogin}
                            isLoading={isLoading}
                            loadingText="Signing in with Google..."
                        >
                            Continue with Google
                        </Button>

                        <HStack width="100%">
                            <Divider />
                            <Text fontSize="sm" color="gray.500" px={2}>
                                OR
                            </Text>
                            <Divider />
                        </HStack>

                        {/* Credentials Form */}
                        <form onSubmit={handleCredentialsLogin} style={{ width: '100%' }}>
                            <VStack spacing={4}>
                                <FormControl isInvalid={!!errors.emailPhoneNumber}>
                                    <FormLabel>Email or Phone Number</FormLabel>
                                    <InputGroup>
                                        <Input
                                            type="text"
                                            placeholder="Enter your email or phone"
                                            value={formData.emailPhoneNumber}
                                            onChange={(e) => handleInputChange('emailPhoneNumber', e.target.value)}
                                            pr="2.5rem"
                                        />
                                        <InputRightElement>
                                            <Icon as={FaEnvelope} color="gray.400" />
                                        </InputRightElement>
                                    </InputGroup>
                                    <FormErrorMessage>{errors.emailPhoneNumber}</FormErrorMessage>
                                </FormControl>

                                <FormControl isInvalid={!!errors.password}>
                                    <FormLabel>Password</FormLabel>
                                    <InputGroup>
                                        <Input
                                            type={showPassword ? 'text' : 'password'}
                                            placeholder="Enter your password"
                                            value={formData.password}
                                            onChange={(e) => handleInputChange('password', e.target.value)}
                                            pr="2.5rem"
                                        />
                                        <InputRightElement>
                                            <IconButton
                                                aria-label={showPassword ? 'Hide password' : 'Show password'}
                                                icon={<Icon as={showPassword ? FaEyeSlash : FaEye} />}
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => setShowPassword(!showPassword)}
                                            />
                                        </InputRightElement>
                                    </InputGroup>
                                    <FormErrorMessage>{errors.password}</FormErrorMessage>
                                </FormControl>

                                <Button
                                    type="submit"
                                    colorScheme="blue"
                                    size="lg"
                                    width="100%"
                                    isLoading={isLoading}
                                    loadingText="Signing in..."
                                >
                                    Sign In
                                </Button>
                            </VStack>
                        </form>

                        {/* Security Notice */}
                        <Alert status="info" borderRadius="md">
                            <AlertIcon />
                            <Box>
                                <AlertTitle fontSize="sm">Enhanced Security!</AlertTitle>
                                <AlertDescription fontSize="xs">
                                    Your login is protected with OWASP-compliant security measures including
                                    rate limiting, input validation, and secure token handling.
                                </AlertDescription>
                            </Box>
                        </Alert>
                    </VStack>
                </CardBody>
            </Card>
        </Box>
    );
};

export default SecureLoginForm;
