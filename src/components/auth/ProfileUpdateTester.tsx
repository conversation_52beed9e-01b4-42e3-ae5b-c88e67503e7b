'use client'
import React, { useState } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    Input,
    FormControl,
    FormLabel,
    Card,
    CardBody,
    CardHeader,
    Heading,
    useToast,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Divider,
    Badge,
    Textarea
} from '@chakra-ui/react';
import { useAuth } from '@/hooks/useAuth';
import { triggerProfileRefresh } from '@/hooks/useProfileSync';
import axios from 'axios';

const ProfileUpdateTester: React.FC = () => {
    const { user, isAuthenticated, secureSession } = useAuth();
    const [isUpdating, setIsUpdating] = useState(false);
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        phoneNumber: '',
    });
    const toast = useToast();

    // Initialize form with current user data
    React.useEffect(() => {
        if (user) {
            setFormData({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                phoneNumber: user.phoneNumber || '',
            });
        }
    }, [user]);

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleUpdateProfile = async () => {
        if (!secureSession.isValid) {
            toast({
                title: 'Error',
                description: 'Invalid session. Please login again.',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        try {
            setIsUpdating(true);

            // Simulate API call to update profile
            // In real app, this would be a PUT/PATCH request to update user profile
            const response = await axios.put(
                `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
                formData,
                {
                    headers: secureSession.getSecureHeaders(),
                }
            );

            if (response.data.status) {
                toast({
                    title: 'Profile Updated',
                    description: 'Your profile has been updated successfully. Changes will sync automatically.',
                    status: 'success',
                    duration: 5000,
                    isClosable: true,
                });

                // Trigger immediate profile refresh
                await triggerProfileRefresh();
            } else {
                throw new Error(response.data.message || 'Update failed');
            }
        } catch (error) {
            console.error('Profile update failed:', error);
            
            toast({
                title: 'Update Failed',
                description: 'Failed to update profile. Please try again.',
                status: 'error',
                duration: 5000,
                isClosable: true,
            });
        } finally {
            setIsUpdating(false);
        }
    };

    const handleForceRefresh = async () => {
        try {
            await triggerProfileRefresh();
            toast({
                title: 'Refresh Triggered',
                description: 'Profile refresh has been triggered. Check for updates.',
                status: 'info',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            toast({
                title: 'Refresh Failed',
                description: 'Failed to trigger profile refresh.',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        }
    };

    if (!isAuthenticated) {
        return (
            <Alert status="warning">
                <AlertIcon />
                <AlertTitle>Not Authenticated</AlertTitle>
                <AlertDescription>Please log in to test profile updates.</AlertDescription>
            </Alert>
        );
    }

    return (
        <Box maxW="800px" mx="auto" p={6}>
            <VStack spacing={6}>
                <Box textAlign="center">
                    <Heading size="lg" mb={2}>
                        🧪 Profile Update Tester
                    </Heading>
                    <Text color="gray.600">
                        Test real-time profile updates without logout/login
                    </Text>
                </Box>

                {/* Current Profile Display */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Current Profile Data</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Text fontWeight="bold" minW="120px">Name:</Text>
                                <Text>{user?.firstName} {user?.lastName}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold" minW="120px">Email:</Text>
                                <Text>{user?.email}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold" minW="120px">Phone:</Text>
                                <Text>{user?.phoneNumber || 'Not set'}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold" minW="120px">Provider:</Text>
                                <Badge colorScheme="blue">
                                    {user?.oauthProvider || 'credentials'}
                                </Badge>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Update Form */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Update Profile</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack spacing={4}>
                            <FormControl>
                                <FormLabel>First Name</FormLabel>
                                <Input
                                    value={formData.firstName}
                                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                                    placeholder="Enter first name"
                                />
                            </FormControl>

                            <FormControl>
                                <FormLabel>Last Name</FormLabel>
                                <Input
                                    value={formData.lastName}
                                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                                    placeholder="Enter last name"
                                />
                            </FormControl>

                            <FormControl>
                                <FormLabel>Phone Number</FormLabel>
                                <Input
                                    value={formData.phoneNumber}
                                    onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                                    placeholder="Enter phone number"
                                />
                            </FormControl>

                            <HStack spacing={4} w="100%">
                                <Button
                                    colorScheme="blue"
                                    onClick={handleUpdateProfile}
                                    isLoading={isUpdating}
                                    loadingText="Updating..."
                                    flex={1}
                                >
                                    Update Profile
                                </Button>
                                <Button
                                    colorScheme="green"
                                    variant="outline"
                                    onClick={handleForceRefresh}
                                    flex={1}
                                >
                                    Force Refresh
                                </Button>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Instructions */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">📋 How to Test</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <Text fontSize="sm">
                                <strong>1.</strong> Update the form fields above with new values
                            </Text>
                            <Text fontSize="sm">
                                <strong>2.</strong> Click "Update Profile" to save changes
                            </Text>
                            <Text fontSize="sm">
                                <strong>3.</strong> Watch the "Current Profile Data" section update automatically
                            </Text>
                            <Text fontSize="sm">
                                <strong>4.</strong> No logout/login required - changes sync in real-time!
                            </Text>
                            <Divider />
                            <Text fontSize="sm" color="blue.600">
                                <strong>Auto-sync:</strong> Profile data refreshes every 30 seconds automatically
                            </Text>
                            <Text fontSize="sm" color="green.600">
                                <strong>Manual refresh:</strong> Use "Force Refresh" to trigger immediate sync
                            </Text>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Technical Details */}
                <Card w="100%" borderColor="gray.200">
                    <CardHeader>
                        <Heading size="sm" color="gray.600">🔧 Technical Details</Heading>
                    </CardHeader>
                    <CardBody>
                        <Textarea
                            value={JSON.stringify({
                                sessionValid: secureSession.isValid,
                                hasAccessToken: !!secureSession.accessToken,
                                currentUser: {
                                    id: user?.id,
                                    firstName: user?.firstName,
                                    lastName: user?.lastName,
                                    email: user?.email,
                                    phoneNumber: user?.phoneNumber,
                                },
                                formData: formData
                            }, null, 2)}
                            readOnly
                            fontSize="xs"
                            fontFamily="mono"
                            rows={10}
                        />
                    </CardBody>
                </Card>

                {/* Real-time Features */}
                <Alert status="success">
                    <AlertIcon />
                    <Box>
                        <AlertTitle>Real-time Profile Sync Active!</AlertTitle>
                        <AlertDescription>
                            Your profile data is automatically synchronized with the server. 
                            Any changes made will be reflected immediately without requiring logout/login.
                        </AlertDescription>
                    </Box>
                </Alert>
            </VStack>
        </Box>
    );
};

export default ProfileUpdateTester;
