'use client'
import React, { useState, useEffect } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Button,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Badge,
    useToast,
    Textarea,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Code,
    Divider,
    Flex
} from '@chakra-ui/react';
import { useAuth } from '@/hooks/useAuth';
import axios from 'axios';

const TokenDebugger: React.FC = () => {
    const { 
        user, 
        isAuthenticated, 
        accessToken,
        refreshToken,
        secureSession 
    } = useAuth();
    
    const [tokenInfo, setTokenInfo] = useState<any>(null);
    const [apiTestResult, setApiTestResult] = useState<any>(null);
    const [isTestingAPI, setIsTestingAPI] = useState(false);
    const toast = useToast();

    // Analyze token when it changes
    useEffect(() => {
        if (accessToken) {
            analyzeToken(accessToken);
        } else {
            setTokenInfo(null);
        }
    }, [accessToken]);

    const analyzeToken = (token: string) => {
        try {
            const parts = token.split('.');
            
            if (parts.length !== 3) {
                setTokenInfo({
                    valid: false,
                    error: 'Token must have 3 parts (header.payload.signature)',
                    parts: parts.length
                });
                return;
            }

            // Decode header and payload
            const header = JSON.parse(atob(parts[0]));
            const payload = JSON.parse(atob(parts[1]));
            
            const now = Math.floor(Date.now() / 1000);
            const isExpired = payload.exp < now;
            const timeUntilExpiry = Math.max(0, payload.exp - now);

            setTokenInfo({
                valid: true,
                header,
                payload,
                signature: parts[2],
                isExpired,
                timeUntilExpiry,
                tokenLength: token.length,
                isEncrypted: token.includes(':'),
                parts: parts.length
            });
        } catch (error) {
            setTokenInfo({
                valid: false,
                error: 'Failed to decode token: ' + (error as Error).message,
                tokenLength: token.length,
                isEncrypted: token.includes(':'),
                parts: token.split('.').length
            });
        }
    };

    const testAPICall = async () => {
        if (!accessToken) {
            toast({
                title: 'No Token',
                description: 'No access token available for testing',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
            return;
        }

        setIsTestingAPI(true);
        setApiTestResult(null);

        try {
            console.log('🧪 Testing API call with token...');
            
            const startTime = Date.now();
            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    },
                }
            );
            const endTime = Date.now();

            setApiTestResult({
                success: true,
                status: response.status,
                data: response.data,
                responseTime: endTime - startTime,
                headers: response.headers,
            });

            toast({
                title: 'API Test Successful',
                description: `Profile retrieved successfully in ${endTime - startTime}ms`,
                status: 'success',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            console.error('API test failed:', error);
            
            const axiosError = error as any;
            setApiTestResult({
                success: false,
                status: axiosError.response?.status || 'Network Error',
                error: axiosError.response?.data || axiosError.message,
                message: axiosError.message,
            });

            toast({
                title: 'API Test Failed',
                description: axiosError.response?.data?.message || axiosError.message,
                status: 'error',
                duration: 5000,
                isClosable: true,
            });
        } finally {
            setIsTestingAPI(false);
        }
    };

    const formatTime = (seconds: number): string => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    if (!isAuthenticated) {
        return (
            <Alert status="warning">
                <AlertIcon />
                <AlertTitle>Not Authenticated</AlertTitle>
                <AlertDescription>Please log in to debug tokens.</AlertDescription>
            </Alert>
        );
    }

    return (
        <Box maxW="1000px" mx="auto" p={6}>
            <VStack spacing={6}>
                <Box textAlign="center">
                    <Heading size="lg" mb={2}>
                        🔍 JWT Token Debugger
                    </Heading>
                    <Text color="gray.600">
                        Debug and validate JWT tokens for API authentication
                    </Text>
                </Box>

                {/* Token Analysis */}
                <Card w="100%">
                    <CardHeader>
                        <Flex justify="space-between" align="center">
                            <Heading size="md">Token Analysis</Heading>
                            <Button
                                colorScheme="blue"
                                size="sm"
                                onClick={testAPICall}
                                isLoading={isTestingAPI}
                                loadingText="Testing..."
                            >
                                Test API Call
                            </Button>
                        </Flex>
                    </CardHeader>
                    <CardBody>
                        {tokenInfo ? (
                            <VStack align="start" spacing={4}>
                                <HStack>
                                    <Text fontWeight="bold">Status:</Text>
                                    <Badge colorScheme={tokenInfo.valid ? 'green' : 'red'}>
                                        {tokenInfo.valid ? 'Valid JWT' : 'Invalid JWT'}
                                    </Badge>
                                </HStack>

                                {tokenInfo.valid ? (
                                    <>
                                        <HStack>
                                            <Text fontWeight="bold">Expired:</Text>
                                            <Badge colorScheme={tokenInfo.isExpired ? 'red' : 'green'}>
                                                {tokenInfo.isExpired ? 'Yes' : 'No'}
                                            </Badge>
                                        </HStack>

                                        {!tokenInfo.isExpired && (
                                            <HStack>
                                                <Text fontWeight="bold">Expires in:</Text>
                                                <Text>{formatTime(tokenInfo.timeUntilExpiry)}</Text>
                                            </HStack>
                                        )}

                                        <HStack>
                                            <Text fontWeight="bold">User ID:</Text>
                                            <Code>{tokenInfo.payload.userId}</Code>
                                        </HStack>

                                        <HStack>
                                            <Text fontWeight="bold">Algorithm:</Text>
                                            <Code>{tokenInfo.header.alg}</Code>
                                        </HStack>

                                        <HStack>
                                            <Text fontWeight="bold">Issued At:</Text>
                                            <Text>{new Date(tokenInfo.payload.iat * 1000).toLocaleString()}</Text>
                                        </HStack>

                                        <HStack>
                                            <Text fontWeight="bold">Expires At:</Text>
                                            <Text>{new Date(tokenInfo.payload.exp * 1000).toLocaleString()}</Text>
                                        </HStack>
                                    </>
                                ) : (
                                    <Alert status="error">
                                        <AlertIcon />
                                        <AlertDescription>{tokenInfo.error}</AlertDescription>
                                    </Alert>
                                )}

                                <HStack>
                                    <Text fontWeight="bold">Token Length:</Text>
                                    <Text>{tokenInfo.tokenLength} characters</Text>
                                </HStack>

                                <HStack>
                                    <Text fontWeight="bold">Parts Count:</Text>
                                    <Text>{tokenInfo.parts}</Text>
                                </HStack>

                                <HStack>
                                    <Text fontWeight="bold">Encrypted:</Text>
                                    <Badge colorScheme={tokenInfo.isEncrypted ? 'orange' : 'green'}>
                                        {tokenInfo.isEncrypted ? 'Yes (Should be No)' : 'No (Correct)'}
                                    </Badge>
                                </HStack>
                            </VStack>
                        ) : (
                            <Text color="gray.500">No token to analyze</Text>
                        )}
                    </CardBody>
                </Card>

                {/* API Test Results */}
                {apiTestResult && (
                    <Card w="100%">
                        <CardHeader>
                            <Heading size="md">API Test Results</Heading>
                        </CardHeader>
                        <CardBody>
                            <VStack align="start" spacing={4}>
                                <HStack>
                                    <Text fontWeight="bold">Status:</Text>
                                    <Badge colorScheme={apiTestResult.success ? 'green' : 'red'}>
                                        {apiTestResult.success ? 'Success' : 'Failed'}
                                    </Badge>
                                </HStack>

                                <HStack>
                                    <Text fontWeight="bold">HTTP Status:</Text>
                                    <Code>{apiTestResult.status}</Code>
                                </HStack>

                                {apiTestResult.responseTime && (
                                    <HStack>
                                        <Text fontWeight="bold">Response Time:</Text>
                                        <Text>{apiTestResult.responseTime}ms</Text>
                                    </HStack>
                                )}

                                <Divider />

                                <Box w="100%">
                                    <Text fontWeight="bold" mb={2}>Response Data:</Text>
                                    <Textarea
                                        value={JSON.stringify(
                                            apiTestResult.success ? apiTestResult.data : apiTestResult.error,
                                            null,
                                            2
                                        )}
                                        readOnly
                                        fontSize="sm"
                                        fontFamily="mono"
                                        rows={10}
                                    />
                                </Box>
                            </VStack>
                        </CardBody>
                    </Card>
                )}

                {/* Raw Token Display */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Raw Token</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={4}>
                            <Box w="100%">
                                <Text fontWeight="bold" mb={2}>Access Token:</Text>
                                <Textarea
                                    value={accessToken || 'No access token'}
                                    readOnly
                                    fontSize="xs"
                                    fontFamily="mono"
                                    rows={4}
                                />
                            </Box>

                            {refreshToken && (
                                <Box w="100%">
                                    <Text fontWeight="bold" mb={2}>Refresh Token:</Text>
                                    <Textarea
                                        value={refreshToken}
                                        readOnly
                                        fontSize="xs"
                                        fontFamily="mono"
                                        rows={4}
                                    />
                                </Box>
                            )}
                        </VStack>
                    </CardBody>
                </Card>

                {/* Instructions */}
                <Alert status="info">
                    <AlertIcon />
                    <Box>
                        <AlertTitle>How to use:</AlertTitle>
                        <AlertDescription>
                            This debugger shows JWT token structure and validates API calls. 
                            The token should be a valid JWT (3 parts separated by dots) and not encrypted.
                            Click "Test API Call" to verify the token works with the backend.
                        </AlertDescription>
                    </Box>
                </Alert>
            </VStack>
        </Box>
    );
};

export default TokenDebugger;
