'use client'
import React, { useEffect, useState } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Badge,
    Button,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Card,
    CardBody,
    CardHeader,
    Heading,
    Divider,
    useToast
} from '@chakra-ui/react';
import { useAuth } from '@/hooks/useAuth';
import GoogleSignInButton from './GoogleSignInButton';
import LoginForm from './LoginForm';

const SecureAuthExample: React.FC = () => {
    const { 
        user, 
        isLoading, 
        isAuthenticated, 
        accessToken, 
        refreshToken,
        isTokenValid,
        logout,
        refreshProfile 
    } = useAuth();
    
    const [securityInfo, setSecurityInfo] = useState({
        tokenLength: 0,
        isEncrypted: false,
        hasFingerprint: false,
        expiresIn: 0
    });
    
    const toast = useToast();

    // Analyze token security (client-side analysis only)
    useEffect(() => {
        if (accessToken) {
            const tokenLength = accessToken.length;
            const isEncrypted = accessToken.includes(':'); // Our encryption format
            const hasFingerprint = tokenLength > 200; // Encrypted tokens are longer
            
            // Calculate expiry (if token is not encrypted)
            let expiresIn = 0;
            if (!isEncrypted) {
                try {
                    const parts = accessToken.split('.');
                    if (parts.length === 3) {
                        const payload = JSON.parse(atob(parts[1]));
                        expiresIn = Math.max(0, payload.exp - Math.floor(Date.now() / 1000));
                    }
                } catch (error) {
                    console.error('Token analysis failed:', error);
                }
            }
            
            setSecurityInfo({
                tokenLength,
                isEncrypted,
                hasFingerprint,
                expiresIn
            });
        } else {
            setSecurityInfo({
                tokenLength: 0,
                isEncrypted: false,
                hasFingerprint: false,
                expiresIn: 0
            });
        }
    }, [accessToken]);

    const handleRefreshProfile = async () => {
        try {
            await refreshProfile();
            toast({
                title: 'Profile Refreshed',
                description: 'Your profile has been updated with the latest data',
                status: 'success',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            toast({
                title: 'Refresh Failed',
                description: 'Failed to refresh profile data',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        }
    };

    const handleLogout = async () => {
        try {
            await logout();
            toast({
                title: 'Logged Out',
                description: 'You have been securely logged out',
                status: 'info',
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            toast({
                title: 'Logout Error',
                description: 'There was an error during logout',
                status: 'error',
                duration: 3000,
                isClosable: true,
            });
        }
    };

    if (isLoading) {
        return (
            <Box textAlign="center" py={10}>
                <Text>Loading secure authentication...</Text>
            </Box>
        );
    }

    if (!isAuthenticated) {
        return (
            <Box maxW="600px" mx="auto" p={6}>
                <VStack spacing={6}>
                    <Box textAlign="center">
                        <Heading size="lg" mb={4}>
                            Secure Authentication Demo
                        </Heading>
                        <Text color="gray.600">
                            Experience enterprise-grade security with encrypted tokens and fingerprinting
                        </Text>
                    </Box>

                    <Alert status="info" borderRadius="lg">
                        <AlertIcon />
                        <Box>
                            <AlertTitle>Security Features!</AlertTitle>
                            <AlertDescription>
                                This demo showcases AES-256 token encryption, client fingerprinting, 
                                rate limiting, and comprehensive security monitoring.
                            </AlertDescription>
                        </Box>
                    </Alert>

                    <LoginForm />
                </VStack>
            </Box>
        );
    }

    return (
        <Box maxW="800px" mx="auto" p={6}>
            <VStack spacing={6}>
                <Box textAlign="center">
                    <Heading size="lg" mb={2}>
                        🔐 Secure Authentication Dashboard
                    </Heading>
                    <Text color="gray.600">
                        Your session is protected with enterprise-grade security
                    </Text>
                </Box>

                {/* User Info Card */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">User Information</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Text fontWeight="bold">Name:</Text>
                                <Text>{user?.name || 'N/A'}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Email:</Text>
                                <Text>{user?.email || 'N/A'}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Phone:</Text>
                                <Text>{user?.phoneNumber || 'N/A'}</Text>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Provider:</Text>
                                <Badge colorScheme="blue">
                                    {user?.oauthProvider || 'credentials'}
                                </Badge>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Security Status Card */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">🛡️ Security Status</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={3}>
                            <HStack>
                                <Text fontWeight="bold">Token Valid:</Text>
                                <Badge colorScheme={isTokenValid ? 'green' : 'red'}>
                                    {isTokenValid ? 'Valid' : 'Invalid'}
                                </Badge>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Token Encrypted:</Text>
                                <Badge colorScheme={securityInfo.isEncrypted ? 'green' : 'orange'}>
                                    {securityInfo.isEncrypted ? 'Yes (AES-256)' : 'No'}
                                </Badge>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Fingerprint Protected:</Text>
                                <Badge colorScheme={securityInfo.hasFingerprint ? 'green' : 'orange'}>
                                    {securityInfo.hasFingerprint ? 'Yes' : 'No'}
                                </Badge>
                            </HStack>
                            <HStack>
                                <Text fontWeight="bold">Token Length:</Text>
                                <Text>{securityInfo.tokenLength} chars</Text>
                            </HStack>
                            {securityInfo.expiresIn > 0 && (
                                <HStack>
                                    <Text fontWeight="bold">Expires In:</Text>
                                    <Text>{Math.floor(securityInfo.expiresIn / 3600)}h {Math.floor((securityInfo.expiresIn % 3600) / 60)}m</Text>
                                </HStack>
                            )}
                        </VStack>
                    </CardBody>
                </Card>

                {/* Security Features Info */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">🔒 Active Security Features</Heading>
                    </CardHeader>
                    <CardBody>
                        <VStack align="start" spacing={2}>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">AES-256-CBC Token Encryption</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Client Fingerprinting</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Rate Limiting Protection</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Automatic Token Rotation</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Security Event Logging</Text>
                            </HStack>
                            <HStack>
                                <Badge colorScheme="green">✓</Badge>
                                <Text fontSize="sm">Suspicious Activity Detection</Text>
                            </HStack>
                        </VStack>
                    </CardBody>
                </Card>

                {/* Actions */}
                <Card w="100%">
                    <CardHeader>
                        <Heading size="md">Actions</Heading>
                    </CardHeader>
                    <CardBody>
                        <HStack spacing={4} wrap="wrap">
                            <Button 
                                colorScheme="blue" 
                                onClick={handleRefreshProfile}
                                size="sm"
                            >
                                Refresh Profile
                            </Button>
                            <Button 
                                colorScheme="red" 
                                variant="outline"
                                onClick={handleLogout}
                                size="sm"
                            >
                                Secure Logout
                            </Button>
                        </HStack>
                    </CardBody>
                </Card>

                {/* Security Notice */}
                <Alert status="success" borderRadius="lg">
                    <AlertIcon />
                    <Box>
                        <AlertTitle>Secure Session Active!</AlertTitle>
                        <AlertDescription>
                            Your session is protected with enterprise-grade security measures. 
                            All tokens are encrypted and your activity is monitored for suspicious behavior.
                        </AlertDescription>
                    </Box>
                </Alert>
            </VStack>
        </Box>
    );
};

export default SecureAuthExample;
