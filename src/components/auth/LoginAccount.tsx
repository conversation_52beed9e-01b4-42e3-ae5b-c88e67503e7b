import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react"
import Link from "next/link"
import { FaGoogle } from "react-icons/fa"
import { useTranslations } from "next-intl"
import FormInputField from "../ui/form/FormInputField"
import { signIn } from "next-auth/react"
import { useForm } from "react-hook-form"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { toaster, Toaster } from "../ui/toaster"
import { useAppStore } from "@/stores/app/store"
import GoogleSignInButton from "./GoogleSignInButton"

type FormLoginValues = {
    email: string
    password: string
}

const LoginAccount: React.FC = () => {
    const t = useTranslations()
    const router = useRouter()
    const { isLoading, setLoading } = useAppStore()

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<FormLoginValues>({
        defaultValues: {
            email: '',
            password: '',
        },
    });

    const onSubmit = async (data: FormLoginValues) => {
        setLoading(true);

        try {
            const result = await signIn("credentials", {
                email: data.email,
                password: data.password,
                redirect: false
            })


            if (result?.ok) {
                toaster.create({
                    title: "Login Successfully",
                    type: "success",
                })
                // router.push('/');
            } else {
                toaster.create({
                    title: "Login Failed",
                    description: result?.error || "Please check your credentials and try again.",
                    type: "error",
                });
            }

        } catch (error) {
            console.error("Login error:", error);
            toaster.create({
                title: "Login Failed",
                description: "An unexpected error occurred. Please try again later.",
                type: "error",
            });
        } finally {
            setLoading(false);
        }
    }
    return (
        <>
            <Heading
                fontSize={'xl'}
                color={'gray.800'}>
                Welcome to King Collectibles
            </Heading>
            <Text
                fontSize={'sm'}
                color={'gray.600'}>
                Sign in or create an account by entering your email below
            </Text>

            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap={4} mt={4}>
                    <FormInputField
                        label="Email or Phone Number"
                        placeholder=""
                        type="text"
                        {...register("email", {
                            required: "Email is required",
                            pattern: {
                                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                                message: "Invalid email format"
                            }
                        })}
                        errorText={errors.email?.message}
                        invalid={!!errors.email}
                    />
                    <FormInputField
                        label="Password"
                        type="password"
                        {...register("password", {
                            required: "Password is required",
                        })}
                        errorText={errors.password?.message}
                        invalid={!!errors.password}
                    />

                    <HStack justifyContent={'end'}>
                        <Link
                            href="/auth/forgot-password">
                            <Text
                                color={'gray.700'}
                                fontSize={'sm'}
                                textDecoration={'underline'}
                            >
                                Forgot password?
                            </Text>
                        </Link>
                    </HStack>
                    <Button
                        type="submit"
                        borderRadius="lg"
                        variant={'solid'}
                        w="full"
                        disabled={isLoading}
                        mt={2}>
                        {isLoading ? <Spinner /> : t("Button.login")}
                    </Button>
                </Stack>
            </form>

            <HStack w="100%" align="center" my={4}>
                <Box flex="1" h="1px" bg="gray.200" />
                <Text px={2} color="gray.500" fontSize="sm">
                    or continue with
                </Text>
                <Box flex="1" h="1px" bg="gray.200" />
            </HStack>

            <GoogleSignInButton/>

            <HStack justifyContent="center" mt={4}>
                <Text fontSize="sm" color="gray.600">
                    Don&apos;t have an account?
                </Text>
                <Link
                    href="/auth/register"
                >
                    <Text
                        color="gray.700"
                        fontSize="sm"
                        fontWeight="semibold"
                        textDecoration="underline"
                        _hover={{ color: 'blue.700' }}
                    >
                        Create an account
                    </Text>
                </Link>
            </HStack>
            <Toaster />
        </>
    )
}

export default LoginAccount