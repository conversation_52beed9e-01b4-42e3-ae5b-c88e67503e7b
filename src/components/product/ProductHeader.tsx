'use client'
import React from 'react';
import { Box, Heading, Text, VStack } from '@chakra-ui/react';
import { ProductItem } from '@/types/product';

interface ProductHeaderProps {
    product: ProductItem;
}

const ProductHeader: React.FC<ProductHeaderProps> = ({ product }) => {
    return (
        <VStack spacing={3} align="stretch">
            <Box>
                <Heading 
                    as="h1"
                    fontSize={{ base: 'lg', md: 'xl', lg: '2xl' }}
                    fontWeight="bold" 
                    lineHeight="shorter"
                    color="gray.800"
                    mb={2}
                >
                    2013-14 Panini Flawless Patch Autographs #PA-KB <PERSON> Bryant Signed Patch Card (#22/25)
                </Heading>
                <Text 
                    fontSize={{ base: 'sm', md: 'md' }}
                    color="gray.500" 
                    fontWeight="semibold"
                >
                    BGS GEM MINT 9.5, Beckett 10
                </Text>
            </Box>
        </VStack>
    );
};

export default ProductHeader;
