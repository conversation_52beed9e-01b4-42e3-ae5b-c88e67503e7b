# 🖼️ ProductCardImage Component

Komponen ProductCardImage yang telah di-refactor untuk mendukung multiple images, modal zoom, swipe gestures, dan responsive design.

## ✨ Features

- ✅ **Multiple Images Support**: Mendukung galeri gambar multiple
- ✅ **Modal Zoom**: Modal fullscreen dengan zoom functionality
- ✅ **Swipe Gestures**: Touch dan mouse swipe untuk navigasi
- ✅ **Responsive Design**: Optimal di semua device
- ✅ **Thumbnail Navigation**: Preview gambar dengan thumbnail
- ✅ **Keyboard Navigation**: Arrow keys, Escape, Spacebar
- ✅ **Accessibility**: Screen reader friendly
- ✅ **Performance Optimized**: Lazy loading dan efficient rendering
- ✅ **Backward Compatible**: Mendukung single image format lama

## 🎯 Props Interface

```typescript
interface ProductCardImageProps {
    item: ProductItem;                              // Product data
    backgroundColorCardImage?: string;              // Background color
    containerProps?: React.ComponentProps<typeof Box>; // Container styling
    imageProps?: React.ComponentProps<typeof Image>;   // Image styling
    boxSizeWatchList?: number;                      // Watchlist icon size
    showThumbnails?: boolean;                       // Show thumbnail navigation
    enableModal?: boolean;                          // Enable modal functionality
    watchlistCount?: number;                        // Watchlist count
    onWatchlistClick?: () => void;                  // Watchlist click handler
}
```

## 📱 Responsive Breakpoints

```typescript
const breakpoints = {
    base: '0px',    // Mobile: Compact thumbnails, touch-optimized
    md: '768px',    // Tablet: Medium thumbnails, hybrid controls
    lg: '992px',    // Desktop: Full thumbnails, mouse-optimized
}
```

## 🎮 Controls & Interactions

### Desktop
- **Click**: Open modal
- **Arrow Keys**: Navigate images in modal
- **Escape**: Close modal
- **Spacebar**: Toggle zoom
- **Mouse Wheel**: Zoom in/out (planned)

### Mobile/Touch
- **Tap**: Open modal
- **Swipe Left/Right**: Navigate images
- **Swipe Up**: Close modal
- **Pinch**: Zoom (planned)
- **Double Tap**: Toggle zoom

## 🔧 Usage Examples

### Basic Usage (Single Image)
```tsx
import ProductCardImage from '@/components/product/ProductCardImage';

const product = {
    id: '1',
    title: 'Sample Product',
    image: '/path/to/image.jpg',
    price: '$100'
};

<ProductCardImage item={product} />
```

### Multiple Images with Full Features
```tsx
const productWithGallery = {
    id: '1',
    title: 'Premium Product',
    image: '/path/to/main.jpg', // Fallback
    images: [
        {
            id: '1',
            url: '/path/to/image1.jpg',
            thumbnail: '/path/to/thumb1.jpg',
            alt: 'Front view'
        },
        {
            id: '2',
            url: '/path/to/image2.jpg',
            thumbnail: '/path/to/thumb2.jpg',
            alt: 'Back view'
        }
    ],
    price: '$500'
};

<ProductCardImage
    item={productWithGallery}
    showThumbnails={true}
    enableModal={true}
    watchlistCount={42}
    onWatchlistClick={() => console.log('Added to watchlist')}
/>
```

### Customized Styling
```tsx
<ProductCardImage
    item={product}
    backgroundColorCardImage="blue.50"
    containerProps={{
        borderRadius: 'xl',
        boxShadow: 'lg'
    }}
    imageProps={{
        filter: 'brightness(1.1)'
    }}
    boxSizeWatchList={6}
/>
```

### Minimal Version (No Modal)
```tsx
<ProductCardImage
    item={product}
    enableModal={false}
    showThumbnails={false}
/>
```

## 🏗️ Component Architecture

```
ProductCardImage
├── Main Image Container
│   ├── Watchlist Button
│   ├── Multiple Images Badge
│   ├── Expand Icon (hover)
│   └── Main Image
├── ImageThumbnails (conditional)
│   ├── Scroll Buttons
│   ├── Thumbnail Grid
│   └── Dots Indicator (mobile)
└── ImageModal (conditional)
    ├── Modal Overlay
    ├── Navigation Arrows
    ├── Zoom Controls
    ├── Image Counter
    └── Mobile Controls
```

## 🎨 Styling Features

### Hover Effects
```css
/* Main container */
transform: scale(1.02);
box-shadow: lg;

/* Watchlist button */
transform: scale(1.05);
background: whiteAlpha.900;

/* Thumbnails */
border-color: blue.500;
transform: scale(1.05);
```

### Responsive Sizing
```typescript
// Thumbnail sizes
const thumbnailSize = {
    sm: { base: '40px', md: '50px' },
    md: { base: '50px', md: '60px' },
    lg: { base: '60px', md: '80px' }
};

// Icon sizes
boxSizeWatchList: { base: 4, md: 4.5, lg: 5 }
```

## 🔌 Custom Hooks

### useImageGallery
```typescript
const {
    currentIndex,
    currentImage,
    isModalOpen,
    isZoomed,
    zoomLevel,
    goToNext,
    goToPrevious,
    goToIndex,
    openModal,
    closeModal,
    toggleZoom,
    setZoomLevel
} = useImageGallery({ images });
```

### useSwipeGesture
```typescript
const { swipeHandlers, isSwiping } = useSwipeGesture({
    onSwipeLeft: goToNext,
    onSwipeRight: goToPrevious,
    onSwipeUp: closeModal,
    threshold: 50
});
```

## 🚀 Performance Optimizations

1. **Lazy Loading**: Images loaded on demand
2. **Thumbnail Optimization**: Separate thumbnail URLs
3. **Memoization**: React.useMemo for expensive calculations
4. **Event Debouncing**: Smooth gesture handling
5. **Efficient Re-renders**: Optimized state updates

## ♿ Accessibility Features

1. **Keyboard Navigation**: Full keyboard support
2. **Screen Reader**: Proper ARIA labels
3. **Focus Management**: Logical tab order
4. **High Contrast**: Visible focus indicators
5. **Semantic HTML**: Proper element structure

## 📊 Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ iOS Safari 14+
- ✅ Chrome Mobile 90+

## 🔄 Migration Guide

### From Old ProductCardImage
```tsx
// Old
<ProductCardImage
    item={product}
    backgroundColorCardImage="gray.100"
    boxSizeWatchList={4.5}
/>

// New (backward compatible)
<ProductCardImage
    item={product}
    backgroundColorCardImage="gray.100"
    boxSizeWatchList={4.5}
    showThumbnails={true}
    enableModal={true}
/>
```

### Update Product Data
```typescript
// Add images array to existing products
const updatedProduct = {
    ...existingProduct,
    images: [
        {
            id: '1',
            url: existingProduct.image,
            thumbnail: existingProduct.image,
            alt: existingProduct.title
        }
    ]
};
```

## 🎯 Best Practices

1. **Image Optimization**: Use WebP format when possible
2. **Thumbnail Generation**: Create optimized thumbnails
3. **Alt Text**: Provide descriptive alt text
4. **Loading States**: Show loading indicators
5. **Error Handling**: Graceful fallbacks for failed images

## 🔮 Future Enhancements

1. **Pinch to Zoom**: Native mobile pinch gestures
2. **Image Lazy Loading**: Intersection Observer
3. **Virtual Scrolling**: For large image sets
4. **Image Filters**: Brightness, contrast, etc.
5. **360° View**: Interactive product rotation
6. **Video Support**: Mixed media galleries
