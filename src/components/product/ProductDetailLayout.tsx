'use client'
import React from 'react';
import { Container, Grid, GridItem } from '@chakra-ui/react';
import { ProductItem } from '@/types/product';
import ProductImageSection from './ProductImageSection';
import ProductInfoSection from './ProductInfoSection';

interface ProductDetailLayoutProps {
    product: ProductItem;
}

const ProductDetailLayout: React.FC<ProductDetailLayoutProps> = ({ product }) => {
    return (
        <Container px={{ base: 0, md: 4 }}>
            <Grid
                templateColumns={{ 
                    base: '1fr', 
                    lg: '1fr 1fr' 
                }}
                gap={{ base: 6, lg: 8 }}
                alignItems="start">
                <GridItem>
                    <ProductImageSection product={product} />
                </GridItem>
                <GridItem>
                    <ProductInfoSection product={product} />
                </GridItem>
            </Grid>
        </Container>
    );
};

export default ProductDetailLayout;
