'use client'
import React from 'react';
import {
    Box,
    Button,
    Icon,
    Image,
    Text,
    VStack,
    Badge,
    useBreakpointValue
} from '@chakra-ui/react';
import { FaRegHeart, FaExpand, FaImages } from 'react-icons/fa';
import { ProductItem, ProductImage } from '@/types/product';
import { useImageGallery } from '@/hooks/useImageGallery';
import ImageModal from '../ui/ImageModal';
import ImageThumbnails from '../ui/ImageThumbnails';

interface ProductCardImageProps {
    item: ProductItem;
    backgroundColorCardImage?: string;
    containerProps?: React.ComponentProps<typeof Box>;
    imageProps?: React.ComponentProps<typeof Image>;
    boxSizeWatchList?: number;
    showThumbnails?: boolean;
    enableModal?: boolean;
    watchlistCount?: number;
    onWatchlistClick?: () => void;
}

const ProductCardImage: React.FC<ProductCardImageProps> = ({
    item,
    backgroundColorCardImage = 'gray.100',
    containerProps = {},
    imageProps = {},
    boxSizeWatchList = 4.5,
    showThumbnails = true,
    enableModal = true,
    watchlistCount = 24,
    onWatchlistClick
}) => {
    const isMobile = useBreakpointValue({ base: true, md: false });

    // Prepare images array - support both single image and multiple images
    const images: ProductImage[] = React.useMemo(() => {
        if (item.images && item.images.length > 0) {
            return item.images;
        }
        return [{
            id: '1',
            url: item.image,
            alt: item.title,
            thumbnail: item.image
        }];
    }, [item.images, item.image, item.title]);

    const {
        currentIndex,
        currentImage,
        isModalOpen,
        isZoomed,
        zoomLevel,
        goToNext,
        goToPrevious,
        goToIndex,
        openModal,
        closeModal,
        toggleZoom,
        setZoomLevel
    } = useImageGallery({ images });

    const hasMultipleImages = images.length > 1;

    return (
        <VStack gap={3} w="100%">
            <Box
                position="relative"
                bg={backgroundColorCardImage}
                display="flex"
                justifyContent="center"
                alignItems="center"
                p={{ base: 4, md: 6 }}
                borderRadius="lg"
                aspectRatio="1/1"
                overflow="hidden"
                cursor={enableModal ? 'pointer' : 'default'}
                transition="all 0.3s ease"
                // _hover={enableModal ? {
                //     transform: 'scale(1.02)',
                //     boxShadow: 'lg'
                // } : {}}
                onClick={enableModal ? () => openModal(currentIndex) : undefined}
                {...containerProps}
            >
                <Box
                    position="absolute"
                    top={{ base: 2, md: 3 }}
                    right={{ base: 2, md: 3 }}
                    zIndex={2}
                >
                    <Button
                        variant="plain"
                        color="gray.600"
                        p={1}
                        minW="auto"
                        h="auto"
                        display="flex"
                        flexDirection="column"
                        gap={0}
                        borderRadius="md"
                        _hover={{
                            bg: 'whiteAlpha.900',
                            transform: 'scale(1.05)'
                        }}
                        onClick={(e) => {
                            e.stopPropagation();
                            onWatchlistClick?.();
                        }}
                        transition="all 0.2s"
                    >
                        <Icon
                            as={FaRegHeart}
                            boxSize={boxSizeWatchList}
                            color="gray.600"
                            _hover={{ color: 'red.500' }}
                            transition="color 0.2s"
                        />
                        <Text fontSize="xs" fontWeight="medium">
                            {watchlistCount}
                        </Text>
                    </Button>
                </Box>

                {/* Multiple Images Indicator */}
                {hasMultipleImages && (
                    <Badge
                        position="absolute"
                        top={{ base: 2, md: 3 }}
                        left={{ base: 2, md: 3 }}
                        bg="blackAlpha.700"
                        color="white"
                        fontSize="xs"
                        borderRadius="md"
                        px={2}
                        py={1}
                        display="flex"
                        alignItems="center"
                        gap={1}
                    >
                        <Icon as={FaImages} boxSize={3} />
                        {images.length}
                    </Badge>
                )}

                {/* Expand Icon for Modal */}
                {enableModal && (
                    <Box
                        position="absolute"
                        bottom={{ base: 2, md: 3 }}
                        right={{ base: 2, md: 3 }}
                        opacity={0}
                        transition="opacity 0.2s"
                        _groupHover={{ opacity: 1 }}
                        _hover={{ opacity: 1 }}
                    >
                        <Icon
                            as={FaExpand}
                            boxSize={4}
                            color="white"
                            bg="blackAlpha.700"
                            p={1}
                            borderRadius="md"
                        />
                    </Box>
                )}

                {/* Main Image */}
                <Image
                    src={currentImage?.url || item.image}
                    alt={currentImage?.alt || item.title}
                    draggable={false}
                    objectFit="contain"
                    maxW="90%"
                    maxH="90%"
                    w="auto"
                    h="auto"
                    transition="transform 0.3s ease"
                    {...imageProps}
                />
            </Box>

            {/* Thumbnails */}
            {showThumbnails && hasMultipleImages && (
                <ImageThumbnails
                    images={images}
                    currentIndex={currentIndex}
                    onSelectImage={goToIndex}
                    size={isMobile ? 'sm' : 'md'}
                    maxVisible={isMobile ? 3 : 5}
                />
            )}

            {/* Image Modal */}
            {enableModal && (
                <ImageModal
                    isOpen={isModalOpen}
                    onClose={closeModal}
                    images={images}
                    currentIndex={currentIndex}
                    onNext={goToNext}
                    onPrevious={goToPrevious}
                    onGoToIndex={goToIndex}
                    isZoomed={isZoomed}
                    zoomLevel={zoomLevel}
                    onToggleZoom={toggleZoom}
                    onSetZoomLevel={setZoomLevel}
                />
            )}
        </VStack>
    );
};

export default ProductCardImage;