'use client'
import React from 'react';
import { 
    Box, 
    Heading, 
    Text, 
    HStack, 
    Icon,
    Link as ChakraLink
} from '@chakra-ui/react';
import { FaArrowUpRightFromSquare } from 'react-icons/fa6';

const SalesHistory: React.FC = () => {
    const handleViewSalesHistory = () => {
        window.open('https://www.cardladder.com/', '_blank', 'noopener,noreferrer');
    };

    return (
        <Box>
            <Heading 
                as="h3" 
                size={{ base: 'sm', md: 'md' }}
                mb={3}
                color="gray.800"
            >
                Sales History
            </Heading>
            
            <Text 
                fontSize={{ base: 'sm', md: 'md' }}
                color="gray.600"
                lineHeight="relaxed"
                mb={4}
            >
                View recent sales history and more about this listing on Goldin trusted partner site:{' '}
                <ChakraLink 
                    href="https://www.cardladder.com/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    color="blue.500"
                    textDecoration="underline"
                    _hover={{ color: 'blue.600' }}
                >
                    cardladder.com
                </ChakraLink>
            </Text>
            
            <HStack
                justify="space-between"
                cursor="pointer"
                bg="gray.50"
                p={4}
                borderRadius="lg"
                onClick={handleViewSalesHistory}
                _hover={{ 
                    bg: 'gray.100',
                    transform: 'translateY(-1px)',
                    boxShadow: 'md'
                }}
                transition="all 0.2s"
                border="1px solid"
                borderColor="gray.200"
            >
                <Text 
                    fontSize={{ base: 'sm', md: 'md' }}
                    fontWeight="medium" 
                    color="gray.800"
                >
                    View Sales History on Card Ladder
                </Text>
                <Icon 
                    as={FaArrowUpRightFromSquare} 
                    boxSize={{ base: 3, md: 4 }}
                    color="gray.600"
                />
            </HStack>
        </Box>
    );
};

export default SalesHistory;
