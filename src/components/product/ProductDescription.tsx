'use client'
import React from 'react';
import { Box, Heading, Text } from '@chakra-ui/react';

const ProductDescription: React.FC = () => {
    return (
        <Box>
            <Heading 
                as="h3" 
                size={{ base: 'sm', md: 'md' }}
                mb={3}
                color="gray.800"
            >
                Description
            </Heading>
            <Text 
                fontSize={{ base: 'sm', md: 'md' }}
                color="gray.600"
                lineHeight="relaxed"
                mb={4}>
                You are viewing one of the thousands of items in our 174th Weekly Sunday Auction, 
                spanning all sports and genres, closing on the evening of May 18th 2025. 
                Extended bidding starts at 7:00PM PT.
            </Text>
            {/* <Divider /> */}
        </Box>
    );
};

export default ProductDescription;
