'use client'

import { ProductItem } from '@/types/product'
import { Box, Flex, Heading, Text, Image } from '@chakra-ui/react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import ProductCardImage from './ProductCardImage'

interface ProductCardProps {
    item: ProductItem
    backgroundColor?: string
    backgroundColorCardImage?: string
    containerProps?: React.ComponentProps<typeof Box>
    imageContainerProps?: React.ComponentProps<typeof Box>
    imageProps?: React.ComponentProps<typeof Image>
    bodyContainerProps?: React.ComponentProps<typeof Box>
}

const ProductCard: React.FC<ProductCardProps> = ({
    item,
    backgroundColor = '',
    backgroundColorCardImage = 'gray.100',
    containerProps = {},
    imageContainerProps = {},
    imageProps = {},
    bodyContainerProps = {},
}) => {
    const t = useTranslations()

    return (
        <Box
            as="article"
            bg={backgroundColor}
            display="flex"
            flexDirection="column"
            w="100%"
            minW={{ base: '160px', sm: '180px', md: '220px', lg: '250px' }}
            maxW={{ base: '180px', sm: '200px', md: '250px', lg: '280px' }}
            gap={{ base: 2, md: 3 }}
            borderRadius="lg"
            overflow="hidden"
            transition="all 0.2s"
            _hover={{
                transform: 'translateY(-2px)',
                boxShadow: 'lg'
            }}
            {...containerProps}
        >
            <Link href={`/auction/${item.slug}`} draggable="false" tabIndex={-1}>
                <ProductCardImage
                    item={item}
                    backgroundColorCardImage={backgroundColorCardImage}
                    containerProps={{
                        borderRadius: 'lg',
                        ...imageContainerProps
                    }}
                    imageProps={imageProps}
                />
            </Link>

            <Box
                p={{ base: 3, md: 4 }}
                w="full"
                {...bodyContainerProps}
            >
                <Link href={`/auction/${item.slug}`} draggable="false">
                    <Text
                        lineClamp={2}
                        fontSize={{ base: 'sm', md: 'md' }}
                        fontWeight="semibold"
                        color="gray.800"
                        _hover={{ textDecoration: 'underline' }}
                        mb={2}
                        minH={{ base: '2.5rem', md: '3rem' }}
                    >
                        {item.title}
                    </Text>
                </Link>

                <Flex
                    mt={2}
                    alignItems="baseline"
                    justify="space-between"
                    flexWrap="wrap"
                    gap={1}
                >
                    <Heading
                        fontWeight="bold"
                        fontSize={{ base: 'lg', md: 'xl', lg: '2xl' }}
                        color="gray.800"
                        lineHeight="shorter"
                    >
                        {item.price}
                    </Heading>
                    <Text
                        color="gray.600"
                        fontSize={{ base: 'xs', md: 'sm' }}
                        fontWeight="medium"
                    >
                        {item.bids ?? '3'} {t('bids')}
                    </Text>
                </Flex>

                <Text
                    mt={2}
                    fontSize={{ base: 'xs', md: 'sm' }}
                    fontWeight="medium"
                    color="gray.500"
                >
                    {item.timeLeft ?? '2d 3h 12m'}
                </Text>
            </Box>
        </Box>
    )
}

export default ProductCard