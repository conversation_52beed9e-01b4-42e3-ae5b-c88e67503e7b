'use client'
import React from 'react';
import { Box, VStack } from '@chakra-ui/react';
import { ProductItem } from '@/types/product';
import ProductHeader from './ProductHeader';
import BidSection from './BidSection';
import ProductDescription from './ProductDescription';
import SalesHistory from './SalesHistory';

interface ProductInfoSectionProps {
    product: ProductItem;
}

const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({ product }) => {
    return (
        <Box
            bg="white"
            p={{ base: 6, md: 8, lg: 12 }}
            borderRadius={{ base: 'sm' }}
            // boxShadow={{ base: 'md', lg: 'lg' }}
            position="sticky"
            top={{ base: 4, md: 8 }}
            // maxH="calc(100vh - 2rem)"
            overflowY="auto"
        >
            <VStack gap={6} align="stretch">
                <ProductHeader product={product} />
                <BidSection />
                <ProductDescription />
                <SalesHistory />
            </VStack>
        </Box>
    );
};

export default ProductInfoSection;
