'use client'
import React from 'react';
import { Box, VStack } from '@chakra-ui/react';
import { ProductItem } from '@/types/product';
import ProductCardImage from './ProductCardImage';
import Breadcrumbs from '../ui/Breadcrumbs';

interface ProductImageSectionProps {
    product: ProductItem;
}

const ProductImageSection: React.FC<ProductImageSectionProps> = ({ product }) => {
    const breadcrumbItems = [
        { label: 'Home', href: '/' },
        { label: 'Auction', href: '/auction' },
        { label: product.title, href: '#', isCurrent: true }
    ];

    return (
        <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumbs items={breadcrumbItems} />
            </Box>
            
            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductCardImage
                    item={product}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: '300px', md: '500px', lg: '600px' },
                        borderRadius: 'lg',
                        overflow: 'hidden'
                    }}
                />
            </Box>
        </VStack>
    );
};

export default ProductImageSection;
