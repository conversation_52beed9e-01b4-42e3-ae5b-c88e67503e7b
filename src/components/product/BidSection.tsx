'use client'
import React from 'react';
import {
    Box,
    Button,
    Flex,
    Heading,
    HStack,
    Icon,
    Text,
    VStack,
} from '@chakra-ui/react';
import { FaClock, FaHistory } from 'react-icons/fa';
import { useProductBid } from '@/hooks/useProductBid';

const BidSection: React.FC = () => {
    const {
        currentBid,
        totalBids,
        timeLeft,
        handlePlaceBid,
        handleShowBidHistory
    } = useProductBid();

    return (
        <VStack gap={6} align="stretch">
            {/* Current Bid and Bids Info */}
            <HStack
                justify="space-between"
                align="start"
                gap={4}
                flexDirection={{ base: 'column', sm: 'row' }}
            >
                <Box flex={1}>
                    <Text
                        fontSize="sm"
                        fontWeight="semibold"
                        color="gray.500"
                        mb={1}
                    >
                        Current Bid:
                    </Text>
                    <Heading
                        size={{ base: '4xl', md: '5xl', lg: '6xl' }}
                        color="black"
                        fontWeight="bold"
                        lineHeight="shorter"
                    >
                        {currentBid}
                    </Heading>
                </Box>

                <Box textAlign={{ base: 'left', sm: 'right' }}>
                    <Text
                        fontSize="sm"
                        fontWeight="semibold"
                        color="gray.500"
                        mb={1}
                    >
                        Bids
                    </Text>
                    <Text
                        fontSize={{ base: '2xl', md: '3xl', lg: '4xl' }}
                        fontWeight="bold"
                        color="gray.800"
                    >
                        {totalBids}
                    </Text>
                    <Text
                        cursor="pointer"
                        fontSize="xs"
                        onClick={handleShowBidHistory}
                        textDecoration="underline"
                        color="gray.600"
                        _hover={{ color: 'gray.800' }}
                        display="flex"
                        alignItems="center"
                        justifyContent={{ base: 'flex-start', sm: 'flex-end' }}
                        mt={1}
                    >
                        <Icon as={FaHistory} boxSize={3} mr={1} />
                        Show bid history
                    </Text>
                </Box>
            </HStack>

            {/* Time Left */}
            <Box>
                <Text
                    fontSize="sm"
                    fontWeight="semibold"
                    color="gray.500"
                    mb={2}
                >
                    Ends in
                </Text>
                <Flex
                    align="center"
                    flexWrap="wrap"
                    gap={2}
                >
                    <Icon as={FaClock} color="gray.600" />
                    <Text
                        color="gray.600"
                        fontWeight="bold"
                        fontSize={{ base: 'sm', md: 'md' }}
                    >
                        {timeLeft}
                    </Text>
                </Flex>
            </Box>

            <Button
                size="lg"
                w="full"
                borderRadius="full"
                colorScheme="blue"
                onClick={handlePlaceBid}
                py={{ base: 6, md: 7 }}
                fontSize={{ base: 'md', md: 'lg' }}
                fontWeight="bold"
                _hover={{
                    transform: 'translateY(-1px)',
                    boxShadow: 'lg'
                }}
                transition="all 0.2s">
                Place Bid
            </Button>

            <Box as={'hr'} borderColor="gray.200" />
        </VStack>
    );
};

export default BidSection;
