'use client'
import React, { useState } from 'react';
import {
    Box,
    Container,
    Heading,
    VStack,
    HStack,
    Text,
    Switch,
    FormControl,
    FormLabel,
    Select,
    Button,
    Grid,
    GridItem,
    useToast
} from '@chakra-ui/react';
import ProductCardImage from './ProductCardImage';
import { sampleProductWithMultipleImages, sampleProductSingleImage, sampleProducts } from '@/utils/sampleProductData';

const ProductCardImageExample: React.FC = () => {
    const [showThumbnails, setShowThumbnails] = useState(true);
    const [enableModal, setEnableModal] = useState(true);
    const [backgroundColorCardImage, setBackgroundColorCardImage] = useState('gray.100');
    const [watchlistCount, setWatchlistCount] = useState(24);
    const toast = useToast();

    const handleWatchlistClick = () => {
        setWatchlistCount(prev => prev + 1);
        toast({
            title: 'Added to Watchlist',
            description: 'Product has been added to your watchlist',
            status: 'success',
            duration: 2000,
            isClosable: true,
        });
    };

    return (
        <Container maxW="8xl" py={8}>
            <VStack spacing={8} align="stretch">
                <Box textAlign="center">
                    <Heading size="xl" mb={4}>
                        ProductCardImage Component Examples
                    </Heading>
                    <Text color="gray.600" fontSize="lg">
                        Interactive examples showcasing multiple images, modal zoom, and swipe gestures
                    </Text>
                </Box>

                {/* Controls */}
                <Box bg="gray.50" p={6} borderRadius="lg">
                    <Heading size="md" mb={4}>Controls</Heading>
                    <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={4}>
                        <FormControl>
                            <FormLabel>Show Thumbnails</FormLabel>
                            <Switch
                                isChecked={showThumbnails}
                                onChange={(e) => setShowThumbnails(e.target.checked)}
                            />
                        </FormControl>
                        
                        <FormControl>
                            <FormLabel>Enable Modal</FormLabel>
                            <Switch
                                isChecked={enableModal}
                                onChange={(e) => setEnableModal(e.target.checked)}
                            />
                        </FormControl>
                        
                        <FormControl>
                            <FormLabel>Background Color</FormLabel>
                            <Select
                                value={backgroundColorCardImage}
                                onChange={(e) => setBackgroundColorCardImage(e.target.value)}
                            >
                                <option value="gray.100">Gray 100</option>
                                <option value="blue.50">Blue 50</option>
                                <option value="green.50">Green 50</option>
                                <option value="purple.50">Purple 50</option>
                                <option value="white">White</option>
                            </Select>
                        </FormControl>
                        
                        <FormControl>
                            <FormLabel>Watchlist Count</FormLabel>
                            <Button
                                size="sm"
                                onClick={() => setWatchlistCount(prev => Math.max(0, prev - 1))}
                                mr={2}
                            >
                                -
                            </Button>
                            <Text as="span" mx={2}>{watchlistCount}</Text>
                            <Button
                                size="sm"
                                onClick={() => setWatchlistCount(prev => prev + 1)}
                            >
                                +
                            </Button>
                        </FormControl>
                    </Grid>
                </Box>

                {/* Examples */}
                <VStack spacing={12} align="stretch">
                    {/* Multiple Images Example */}
                    <Box>
                        <Heading size="lg" mb={4}>Multiple Images Gallery</Heading>
                        <Text color="gray.600" mb={6}>
                            Product with 5 images, thumbnails, modal zoom, and swipe navigation
                        </Text>
                        <Grid templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }} gap={8}>
                            <GridItem>
                                <Box maxW="400px" mx="auto">
                                    <ProductCardImage
                                        item={sampleProductWithMultipleImages}
                                        backgroundColorCardImage={backgroundColorCardImage}
                                        showThumbnails={showThumbnails}
                                        enableModal={enableModal}
                                        watchlistCount={watchlistCount}
                                        onWatchlistClick={handleWatchlistClick}
                                    />
                                </Box>
                            </GridItem>
                            <GridItem>
                                <VStack align="start" spacing={3}>
                                    <Text><strong>Features:</strong></Text>
                                    <Text>• 5 high-quality images</Text>
                                    <Text>• Thumbnail navigation</Text>
                                    <Text>• Modal with zoom (click image)</Text>
                                    <Text>• Swipe gestures (touch devices)</Text>
                                    <Text>• Keyboard navigation (arrow keys, escape, space)</Text>
                                    <Text>• Responsive design</Text>
                                    <Text>• Watchlist functionality</Text>
                                </VStack>
                            </GridItem>
                        </Grid>
                    </Box>

                    {/* Single Image Example */}
                    <Box>
                        <Heading size="lg" mb={4}>Single Image (Backward Compatible)</Heading>
                        <Text color="gray.600" mb={6}>
                            Traditional single image format with modal support
                        </Text>
                        <Grid templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }} gap={8}>
                            <GridItem>
                                <Box maxW="400px" mx="auto">
                                    <ProductCardImage
                                        item={sampleProductSingleImage}
                                        backgroundColorCardImage={backgroundColorCardImage}
                                        showThumbnails={showThumbnails}
                                        enableModal={enableModal}
                                        watchlistCount={watchlistCount}
                                        onWatchlistClick={handleWatchlistClick}
                                    />
                                </Box>
                            </GridItem>
                            <GridItem>
                                <VStack align="start" spacing={3}>
                                    <Text><strong>Features:</strong></Text>
                                    <Text>• Single image display</Text>
                                    <Text>• No thumbnails (automatically hidden)</Text>
                                    <Text>• Modal zoom available</Text>
                                    <Text>• Backward compatible with old data format</Text>
                                    <Text>• Same responsive behavior</Text>
                                </VStack>
                            </GridItem>
                        </Grid>
                    </Box>

                    {/* Minimal Example */}
                    <Box>
                        <Heading size="lg" mb={4}>Minimal Version</Heading>
                        <Text color="gray.600" mb={6}>
                            Simplified version without modal or thumbnails
                        </Text>
                        <Grid templateColumns={{ base: '1fr', lg: 'repeat(2, 1fr)' }} gap={8}>
                            <GridItem>
                                <Box maxW="400px" mx="auto">
                                    <ProductCardImage
                                        item={sampleProducts[2]}
                                        backgroundColorCardImage={backgroundColorCardImage}
                                        showThumbnails={false}
                                        enableModal={false}
                                        watchlistCount={watchlistCount}
                                        onWatchlistClick={handleWatchlistClick}
                                    />
                                </Box>
                            </GridItem>
                            <GridItem>
                                <VStack align="start" spacing={3}>
                                    <Text><strong>Features:</strong></Text>
                                    <Text>• No modal functionality</Text>
                                    <Text>• No thumbnails</Text>
                                    <Text>• Static image display</Text>
                                    <Text>• Watchlist still functional</Text>
                                    <Text>• Lightweight version</Text>
                                </VStack>
                            </GridItem>
                        </Grid>
                    </Box>

                    {/* Grid Layout Example */}
                    <Box>
                        <Heading size="lg" mb={4}>Grid Layout</Heading>
                        <Text color="gray.600" mb={6}>
                            Multiple products in responsive grid
                        </Text>
                        <Grid 
                            templateColumns={{ 
                                base: 'repeat(2, 1fr)', 
                                md: 'repeat(3, 1fr)', 
                                lg: 'repeat(4, 1fr)' 
                            }} 
                            gap={6}
                        >
                            {sampleProducts.map((product) => (
                                <GridItem key={product.id}>
                                    <ProductCardImage
                                        item={product}
                                        backgroundColorCardImage={backgroundColorCardImage}
                                        showThumbnails={showThumbnails}
                                        enableModal={enableModal}
                                        watchlistCount={Math.floor(Math.random() * 50) + 10}
                                        onWatchlistClick={handleWatchlistClick}
                                        containerProps={{
                                            height: { base: '200px', md: '250px' }
                                        }}
                                    />
                                </GridItem>
                            ))}
                        </Grid>
                    </Box>
                </VStack>

                {/* Instructions */}
                <Box bg="blue.50" p={6} borderRadius="lg">
                    <Heading size="md" mb={4}>How to Test</Heading>
                    <VStack align="start" spacing={2}>
                        <Text><strong>Desktop:</strong></Text>
                        <Text>• Click any image to open modal</Text>
                        <Text>• Use arrow keys to navigate in modal</Text>
                        <Text>• Press spacebar to toggle zoom</Text>
                        <Text>• Press escape to close modal</Text>
                        
                        <Text mt={4}><strong>Mobile/Touch:</strong></Text>
                        <Text>• Tap image to open modal</Text>
                        <Text>• Swipe left/right to navigate</Text>
                        <Text>• Swipe up to close modal</Text>
                        <Text>• Tap image in modal to toggle zoom</Text>
                        
                        <Text mt={4}><strong>Thumbnails:</strong></Text>
                        <Text>• Click/tap thumbnails to switch images</Text>
                        <Text>• Scroll arrows appear when needed</Text>
                        <Text>• Active thumbnail is highlighted</Text>
                    </VStack>
                </Box>
            </VStack>
        </Container>
    );
};

export default ProductCardImageExample;
