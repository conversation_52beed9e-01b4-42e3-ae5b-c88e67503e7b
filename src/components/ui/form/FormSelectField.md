# FormSelectField Component

Komponen FormSelectField yang reusable dan maintainable menggunakan react-select dengan styling yang konsisten dengan Chakra UI.

## Features

- ✅ **Reusable**: Interface yang fleksibel untuk berbagai use case
- ✅ **Maintainable**: Code yang bersih dan mudah dipahami
- ✅ **TypeScript Support**: Full type safety
- ✅ **Single & Multi Select**: Mendukung pemilihan tunggal dan multiple
- ✅ **Controlled & Uncontrolled**: Mendukung kedua mode
- ✅ **Validation**: Integrasi dengan form validation
- ✅ **Accessible**: Mengikuti standar accessibility
- ✅ **Consistent Styling**: Styling yang konsisten dengan design system

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label untuk field |
| `description` | `string` | - | Deskripsi tambahan di bawah label |
| `required` | `boolean` | `false` | Menandai field sebagai required |
| `errorText` | `string` | - | Pesan error yang ditampilkan |
| `placeholder` | `string` | `"Select an option"` | Placeholder text |
| `invalid` | `boolean` | `false` | Menandai field dalam state error |
| `options` | `SelectOption[]` | - | Array opsi untuk select |
| `onChange` | `function` | - | Callback ketika value berubah |
| `defaultValue` | `SelectOption \| SelectOption[]` | - | Default value (uncontrolled) |
| `value` | `SelectOption \| SelectOption[]` | - | Current value (controlled) |
| `width` | `string` | `"100%"` | Lebar komponen |
| `name` | `string` | - | Name attribute untuk form |
| `disabled` | `boolean` | `false` | Disable komponen |
| `isMulti` | `boolean` | `false` | Enable multiple selection |
| `isClearable` | `boolean` | `true` | Enable clear button |
| `isSearchable` | `boolean` | `true` | Enable search functionality |
| `menuPortalTarget` | `HTMLElement` | - | Portal target untuk dropdown |

## SelectOption Interface

```typescript
interface SelectOption {
    value: string;
    label: string;
    isDisabled?: boolean;
}
```

## Basic Usage

### Single Select

```tsx
import FormSelectField, { SelectOption } from './FormSelectField';

const options: SelectOption[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
];

<FormSelectField
    label="Choose Option"
    placeholder="Select an option..."
    options={options}
    onChange={(selectedOption) => {
        console.log('Selected:', selectedOption);
    }}
    required
/>
```

### Multi Select

```tsx
<FormSelectField
    label="Choose Multiple Options"
    placeholder="Select options..."
    options={options}
    isMulti
    onChange={(selectedOptions) => {
        console.log('Selected:', selectedOptions);
    }}
/>
```

### With Form Validation

```tsx
<FormSelectField
    label="Category"
    options={categoryOptions}
    value={selectedCategory}
    onChange={setSelectedCategory}
    invalid={!!errors.category}
    errorText={errors.category?.message}
    required
/>
```

### Controlled vs Uncontrolled

```tsx
// Controlled
const [value, setValue] = useState<SelectOption | null>(null);
<FormSelectField
    value={value}
    onChange={setValue}
    options={options}
/>

// Uncontrolled
<FormSelectField
    defaultValue={options[0]}
    onChange={(value) => console.log(value)}
    options={options}
/>
```

## Styling

Komponen menggunakan custom styles yang konsisten dengan Chakra UI theme:

- **Focus state**: Blue border dengan box-shadow
- **Error state**: Red border
- **Hover state**: Gray border
- **Selected option**: Blue background
- **Disabled state**: Gray background

## Migration dari Versi Lama

Jika Anda menggunakan versi lama dengan ChakraSelect:

```tsx
// Lama
<FormSelectField
    collection={createListCollection({ items: options })}
    onValueChange={(details) => console.log(details)}
/>

// Baru
<FormSelectField
    options={options}
    onChange={(selectedOption) => console.log(selectedOption)}
/>
```

## Best Practices

1. **Gunakan TypeScript**: Selalu define type untuk options
2. **Handle loading state**: Tampilkan loading indicator saat fetch data
3. **Optimize large lists**: Gunakan virtualization untuk list besar
4. **Accessibility**: Pastikan label dan description jelas
5. **Error handling**: Selalu handle error state dengan pesan yang jelas

## Troubleshooting

### Dropdown tidak muncul
- Pastikan `menuPortalTarget` di-set jika ada z-index issues
- Gunakan `menuPosition="fixed"` untuk positioning yang lebih baik

### Performance issues dengan banyak options
- Pertimbangkan menggunakan pagination atau search
- Implement lazy loading untuk data besar

### Styling tidak sesuai
- Check apakah ada CSS conflicts
- Pastikan Chakra UI theme ter-load dengan benar
