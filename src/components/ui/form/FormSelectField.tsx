import { Field, ListCollection, type SelectValueChangeDetails } from "@chakra-ui/react";
import React from "react";
import { ChakraSelect, SelectItem } from "../select/ChakraSelect";

interface FormSelectFieldProps {
    label?: string;
    description?: string;
    required?: boolean;
    errorText?: string;
    placeholder?: string;
    invalid?: boolean;
    portalRef?: React.RefObject<HTMLDivElement>;
    collection: ListCollection<SelectItem>;
    onValueChange?: (details: SelectValueChangeDetails) => void;
    defaultValue?: string[];
    value?: string[];
    width?: string;
    size?: "sm" | "md" | "lg";
    name?: string;
    disabled?: boolean;
    multiple?: boolean;
}

const FormSelectField = React.forwardRef<HTMLDivElement, FormSelectFieldProps>(
    (
        {
            label,
            description,
            required = false,
            errorText,
            placeholder = "Select an option",
            invalid = false,
            portalRef,
            collection,
            onValueChange,
            defaultValue = [],
            value,
            width = "100%",
            size = "sm",
            name,
            disabled = false,
            multiple = false
        },
        ref
    ) => {
        // Handle controlled vs uncontrolled mode
        const isControlled = value !== undefined;
        const selectValue = isControlled ? value : defaultValue;

        const handleValueChange = (details: SelectValueChangeDetails) => {
            if (onValueChange) {
                onValueChange(details);
            }
        };

        return (
            <Field.Root required={required} invalid={invalid} ref={ref}>
                {label && (
                    <Field.Label fontWeight="bold" color="gray.600">
                        {label}
                        {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
                    </Field.Label>
                )}
                {description && (
                    <Field.Label color="gray.500" fontSize="sm">
                        {description}
                    </Field.Label>
                )}
                <ChakraSelect
                    collection={collection}
                    placeholder={placeholder}
                    width={width}
                    size={size}
                    defaultValue={!isControlled ? selectValue : undefined}
                    value={isControlled ? selectValue : undefined}
                    onValueChange={handleValueChange}
                    portalRef={portalRef}
                    disabled={disabled}
                    multiple={multiple}
                    name={name}
                />
                {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
            </Field.Root>
        );
    }
);

FormSelectField.displayName = "FormSelectField";

export default FormSelectField;
