import { Field, Input, ListCollection, type SelectValueChangeDetails, } from "@chakra-ui/react";
import { PasswordInput } from "../password-input";
import React from "react";
import { ChakraSelect, SelectItem } from "../select/ChakraSelect";
import Select from 'react-select'

interface FormSelectFieldProps {
    label?: string;
    description?: string;
    required?: boolean;
    errorText?: string;
    placeholder?: string;
    invalid?: boolean;
    portalRef?: React.RefObject<HTMLDivElement>;
    collection: ListCollection<SelectItem>;
    onValueChange?: (details: SelectValueChangeDetails) => void;
    defaultValue?: string[];
    width?: string;
    size?: "sm" | "md" | "lg";
}

const FormInputField = React.forwardRef<HTMLInputElement, FormSelectFieldProps>(
    (
        {
            label,
            description,
            required = false,
            errorText,
            placeholder = "",
            invalid = false,
            portalRef,
            collection,
            onValueChange,
            defaultValue = [],
            width = "280px",
            size = "sm"
        },
        ref
    ) => {

        return (
            <Field.Root required={required} invalid={invalid}>
                <Field.Label fontWeight="bold" color="gray.600">
                    {label}
                    {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
                </Field.Label>
                {description && (
                    <Field.Label color={"gray.500"}>{description}</Field.Label>
                )}
                <Select
                    options={[]}
                    placeholder={placeholder}
                    isClearable
                    styles={{
                        control: (baseStyles) => ({
                            ...baseStyles,
                            borderColor: "#e5e7eb",
                            boxShadow: "none",
                            padding: "4px 6px",
                            borderRadius: "5px",
                        }),
                        container: (baseStyles) => ({
                            ...baseStyles,
                            width: "100%",
                        }),
                        option: (baseStyles, state) => ({
                            ...baseStyles,
                            backgroundColor:
                                state.isSelected
                                    ? "#EDF2F7"
                                    : "#fff",
                            color: "#2D3748",
                            "&:hover": {
                                backgroundColor: "#EDF2F7",
                            },
                            "&:active": {
                                backgroundColor: "#EDF2F7",
                            },
                            '&[data-selected="true"]': {
                                backgroundColor: "#EDF2F7",
                            },
                        }),
                        menu: (baseStyles) => ({
                            ...baseStyles,
                            border: "1px solid #e5e7eb",
                            boxShadow:
                                "0 1px 2px 0 rgba(0, 0, 0, 0.10)",
                            zIndex: 9999,
                        }),
                    }}
                    onChange={async (value) => {
                 
                    }}
                />
                {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
            </Field.Root>
        );
    }
);

FormInputField.displayName = "FormInputField";

export default FormInputField;
