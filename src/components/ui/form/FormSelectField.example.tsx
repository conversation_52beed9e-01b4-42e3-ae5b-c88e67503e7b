import React, { useState } from 'react';
import { Box, Stack, Button, Text } from '@chakra-ui/react';
import FormS<PERSON>ct<PERSON>ield, { SelectOption } from './FormSelectField';
import { SingleValue, MultiValue } from 'react-select';

// Example usage of FormSelectField component
const FormSelectFieldExample: React.FC = () => {
    // Single select state
    const [selectedCategory, setSelectedCategory] = useState<SelectOption | null>(null);
    
    // Multi select state
    const [selectedTags, setSelectedTags] = useState<SelectOption[]>([]);
    
    // Options for single select
    const categoryOptions: SelectOption[] = [
        { value: 'electronics', label: 'Electronics' },
        { value: 'clothing', label: 'Clothing' },
        { value: 'books', label: 'Books' },
        { value: 'sports', label: 'Sports & Outdoors' },
        { value: 'home', label: 'Home & Garden' },
    ];
    
    // Options for multi select
    const tagOptions: SelectOption[] = [
        { value: 'new', label: 'New' },
        { value: 'popular', label: 'Popular' },
        { value: 'sale', label: 'On Sale' },
        { value: 'featured', label: 'Featured' },
        { value: 'limited', label: 'Limited Edition' },
    ];
    
    // Options with disabled items
    const statusOptions: SelectOption[] = [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'pending', label: 'Pending', isDisabled: true },
        { value: 'archived', label: 'Archived' },
    ];

    const handleCategoryChange = (
        newValue: SingleValue<SelectOption>
    ) => {
        setSelectedCategory(newValue);
        console.log('Category changed:', newValue);
    };

    const handleTagsChange = (
        newValue: MultiValue<SelectOption>
    ) => {
        setSelectedTags([...newValue]);
        console.log('Tags changed:', newValue);
    };

    const handleSubmit = () => {
        console.log('Form data:', {
            category: selectedCategory,
            tags: selectedTags,
        });
    };

    return (
        <Box maxW="600px" mx="auto" p={6}>
            <Text fontSize="2xl" fontWeight="bold" mb={6}>
                FormSelectField Examples
            </Text>
            
            <Stack spacing={6}>
                {/* Basic single select */}
                <FormSelectField
                    label="Product Category"
                    description="Choose the main category for your product"
                    placeholder="Select a category..."
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    required
                />

                {/* Multi select */}
                <FormSelectField
                    label="Product Tags"
                    description="Select multiple tags that apply to your product"
                    placeholder="Choose tags..."
                    options={tagOptions}
                    value={selectedTags}
                    onChange={handleTagsChange}
                    isMulti
                    required
                />

                {/* With error state */}
                <FormSelectField
                    label="Status"
                    placeholder="Select status..."
                    options={statusOptions}
                    invalid
                    errorText="Please select a valid status"
                />

                {/* Disabled select */}
                <FormSelectField
                    label="Disabled Field"
                    placeholder="This field is disabled"
                    options={categoryOptions}
                    disabled
                />

                {/* Non-clearable and non-searchable */}
                <FormSelectField
                    label="Fixed Options"
                    placeholder="Select an option..."
                    options={categoryOptions}
                    isClearable={false}
                    isSearchable={false}
                />

                {/* With default value */}
                <FormSelectField
                    label="Pre-selected Category"
                    placeholder="Select a category..."
                    options={categoryOptions}
                    defaultValue={categoryOptions[0]}
                />

                {/* Custom width */}
                <FormSelectField
                    label="Custom Width"
                    placeholder="This field has custom width"
                    options={categoryOptions}
                    width="300px"
                />

                <Button 
                    colorScheme="blue" 
                    onClick={handleSubmit}
                    mt={4}
                >
                    Submit Form
                </Button>
            </Stack>
        </Box>
    );
};

export default FormSelectFieldExample;
