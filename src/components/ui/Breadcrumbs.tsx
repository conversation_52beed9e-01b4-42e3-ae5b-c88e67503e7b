'use client'
import React from 'react';
import { Breadcrumb, Text } from '@chakra-ui/react';
import Link from 'next/link';

interface BreadcrumbItem {
    label: string;
    href: string;
    isCurrent?: boolean;
}

interface BreadcrumbsProps {
    items: BreadcrumbItem[];
    maxWidth?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ 
    items, 
    maxWidth = "600px" 
}) => {
    return (
        <Breadcrumb.Root 
            whiteSpace="nowrap" 
            mb={{ base: 2, md: 4 }}
        >
            <Breadcrumb.List>
                {items.map((item, index) => (
                    <React.Fragment key={index}>
                        <Breadcrumb.Item>
                            {item.isCurrent ? (
                                <Breadcrumb.CurrentLink>
                                    <Text 
                                        lineClamp={1} 
                                        maxW={maxWidth} 
                                        fontSize="sm" 
                                        fontWeight="bold" 
                                        color="gray.600"
                                    >
                                        {item.label}
                                    </Text>
                                </Breadcrumb.CurrentLink>
                            ) : (
                                <Breadcrumb.Link asChild>
                                    <Link href={item.href}>
                                        {item.label}
                                    </Link>
                                </Breadcrumb.Link>
                            )}
                        </Breadcrumb.Item>
                        {index < items.length - 1 && <Breadcrumb.Separator />}
                    </React.Fragment>
                ))}
            </Breadcrumb.List>
        </Breadcrumb.Root>
    );
};

export default Breadcrumbs;
