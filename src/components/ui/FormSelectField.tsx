import React from 'react';
import {
  FormControl,
  FormLabel,
  FormErrorMessage,
  Select,
  SelectProps,
} from '@chakra-ui/react';

interface Option {
  value: string;
  label: string;
}

interface FormSelectFieldProps extends Omit<SelectProps, 'children'> {
  label: string;
  error?: string;
  options: Option[];
  placeholder?: string;
  isRequired?: boolean;
}

const FormSelectField: React.FC<FormSelectFieldProps> = ({
  label,
  error,
  options,
  placeholder = "Select an option",
  isRequired = false,
  ...selectProps
}) => {
  return (
    <FormControl isInvalid={!!error} isRequired={isRequired}>
      <FormLabel>{label}</FormLabel>
      <Select placeholder={placeholder} {...selectProps}>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>
      {error && <FormErrorMessage>{error}</FormErrorMessage>}
    </FormControl>
  );
};

export default FormSelectField;
