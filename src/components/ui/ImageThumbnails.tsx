'use client'
import React from 'react';
import {
    Box,
    HStack,
    Image,
    IconButton,
    useBreakpointValue
} from '@chakra-ui/react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { ProductImage } from '@/types/product';

interface ImageThumbnailsProps {
    images: ProductImage[];
    currentIndex: number;
    onSelectImage: (index: number) => void;
    maxVisible?: number;
    size?: 'sm' | 'md' | 'lg';
}

const ImageThumbnails: React.FC<ImageThumbnailsProps> = ({
    images,
    currentIndex,
    onSelectImage,
    maxVisible = 5,
    size = 'md'
}) => {
    const isMobile = useBreakpointValue({ base: true, md: false });
    const [startIndex, setStartIndex] = React.useState(0);

    // Responsive thumbnail sizes
    const thumbnailSize = {
        sm: { base: '40px', md: '50px' },
        md: { base: '50px', md: '60px' },
        lg: { base: '60px', md: '80px' }
    }[size];

    // Calculate visible range
    const visibleCount = isMobile ? Math.min(maxVisible - 2, 3) : maxVisible;
    const endIndex = Math.min(startIndex + visibleCount, images.length);
    const visibleImages = images.slice(startIndex, endIndex);

    // Navigation functions
    const canScrollLeft = startIndex > 0;
    const canScrollRight = endIndex < images.length;

    const scrollLeft = () => {
        setStartIndex(Math.max(0, startIndex - 1));
    };

    const scrollRight = () => {
        setStartIndex(Math.min(images.length - visibleCount, startIndex + 1));
    };

    // Auto-scroll to keep current image visible
    React.useEffect(() => {
        if (currentIndex < startIndex) {
            setStartIndex(currentIndex);
        } else if (currentIndex >= endIndex) {
            setStartIndex(Math.max(0, currentIndex - visibleCount + 1));
        }
    }, [currentIndex, startIndex, endIndex, visibleCount]);

    if (images.length <= 1) return null;

    return (
        <Box
            position="relative"
            w="100%"
            py={2}
        >
            <HStack
                spacing={2}
                justify="center"
                align="center"
                w="100%"
            >
                {/* Left scroll button */}
                {canScrollLeft && (
                    <IconButton
                        aria-label="Scroll thumbnails left"
                        icon={<FaChevronLeft />}
                        size="sm"
                        variant="ghost"
                        color="gray.600"
                        _hover={{ color: 'gray.800', bg: 'gray.100' }}
                        onClick={scrollLeft}
                        borderRadius="full"
                    />
                )}

                {/* Thumbnails */}
                <HStack spacing={2} overflow="hidden">
                    {visibleImages.map((image, index) => {
                        const actualIndex = startIndex + index;
                        const isActive = actualIndex === currentIndex;
                        
                        return (
                            <Box
                                key={image.id}
                                position="relative"
                                cursor="pointer"
                                onClick={() => onSelectImage(actualIndex)}
                                borderRadius="md"
                                overflow="hidden"
                                border="2px solid"
                                borderColor={isActive ? 'blue.500' : 'transparent'}
                                transition="all 0.2s"
                                _hover={{
                                    borderColor: isActive ? 'blue.600' : 'gray.300',
                                    transform: 'scale(1.05)'
                                }}
                            >
                                <Image
                                    src={image.thumbnail || image.url}
                                    alt={image.alt || `Thumbnail ${actualIndex + 1}`}
                                    w={thumbnailSize}
                                    h={thumbnailSize}
                                    objectFit="cover"
                                    draggable={false}
                                    opacity={isActive ? 1 : 0.7}
                                    transition="opacity 0.2s"
                                    _hover={{ opacity: 1 }}
                                />
                                
                                {/* Active indicator */}
                                {isActive && (
                                    <Box
                                        position="absolute"
                                        top={0}
                                        left={0}
                                        right={0}
                                        bottom={0}
                                        bg="blue.500"
                                        opacity={0.2}
                                        pointerEvents="none"
                                    />
                                )}
                            </Box>
                        );
                    })}
                </HStack>

                {/* Right scroll button */}
                {canScrollRight && (
                    <IconButton
                        aria-label="Scroll thumbnails right"
                        icon={<FaChevronRight />}
                        size="sm"
                        variant="ghost"
                        color="gray.600"
                        _hover={{ color: 'gray.800', bg: 'gray.100' }}
                        onClick={scrollRight}
                        borderRadius="full"
                    />
                )}
            </HStack>

            {/* Dots indicator for mobile when there are many images */}
            {isMobile && images.length > 5 && (
                <HStack
                    justify="center"
                    mt={2}
                    spacing={1}
                >
                    {Array.from({ length: Math.ceil(images.length / visibleCount) }).map((_, index) => {
                        const isActiveDot = Math.floor(currentIndex / visibleCount) === index;
                        return (
                            <Box
                                key={index}
                                w={2}
                                h={2}
                                borderRadius="full"
                                bg={isActiveDot ? 'blue.500' : 'gray.300'}
                                transition="background-color 0.2s"
                            />
                        );
                    })}
                </HStack>
            )}
        </Box>
    );
};

export default ImageThumbnails;
