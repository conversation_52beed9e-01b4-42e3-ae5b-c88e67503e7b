'use client'
import React, { useEffect } from 'react';
import {
    // Modal,
    // ModalOverlay,
    // ModalContent,
    // ModalBody,
    // ModalCloseButton,
    Box,
    Image,
    IconButton,
    HStack,
    Text,
    VStack,
    useBreakpointValue
} from '@chakra-ui/react';
import {
    FaChevronLeft,
    FaChevronRight,
    FaSearchPlus,
    FaSearchMinus,
    FaExpand,
    FaCompress
} from 'react-icons/fa';
import { ProductImage } from '@/types/product';
import { useSwipeGesture } from '@/hooks/useSwipeGesture';

interface ImageModalProps {
    isOpen: boolean;
    onClose: () => void;
    images: ProductImage[];
    currentIndex: number;
    onNext: () => void;
    onPrevious: () => void;
    onGoToIndex: (index: number) => void;
    isZoomed: boolean;
    zoomLevel: number;
    onToggleZoom: () => void;
    onSetZoomLevel: (level: number) => void;
}

const ImageModal: React.FC<ImageModalProps> = ({
    isOpen,
    onClose,
    images,
    currentIndex,
    onNext,
    onPrevious,
    onGoToIndex,
    isZoomed,
    zoomLevel,
    onToggleZoom,
    onSetZoomLevel
}) => {
    const isMobile = useBreakpointValue({ base: true, md: false });
    const currentImage = images[currentIndex];

    const { swipeHandlers } = useSwipeGesture({
        onSwipeLeft: onNext,
        onSwipeRight: onPrevious,
        onSwipeUp: onClose,
        threshold: 50
    });

    // Prevent body scroll when modal is open
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }
        
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    if (!currentImage) return null;

    const handleZoomIn = () => {
        const newZoom = Math.min(zoomLevel + 0.5, 4);
        onSetZoomLevel(newZoom);
    };

    const handleZoomOut = () => {
        const newZoom = Math.max(zoomLevel - 0.5, 1);
        onSetZoomLevel(newZoom);
    };

    return (
        <></>
        // <Modal 
        //     isOpen={isOpen} 
        //     onClose={onClose} 
        //     size="full"
        //     motionPreset="slideInBottom"
        // >
        //     <ModalOverlay bg="blackAlpha.900" />
        //     <ModalContent bg="transparent" boxShadow="none">
        //         <ModalCloseButton
        //             position="fixed"
        //             top={4}
        //             right={4}
        //             zIndex={1000}
        //             bg="blackAlpha.600"
        //             color="white"
        //             _hover={{ bg: 'blackAlpha.800' }}
        //             size="lg"
        //         />

        //         <ModalBody p={0} position="relative" h="100vh">
        //             {/* Main Image Container */}
        //             <Box
        //                 position="relative"
        //                 h="100%"
        //                 display="flex"
        //                 alignItems="center"
        //                 justifyContent="center"
        //                 {...swipeHandlers}
        //                 cursor={isZoomed ? 'zoom-out' : 'zoom-in'}
        //                 onClick={onToggleZoom}
        //             >
        //                 <Image
        //                     src={currentImage.url}
        //                     alt={currentImage.alt || `Image ${currentIndex + 1}`}
        //                     maxW={isZoomed ? 'none' : '90vw'}
        //                     maxH={isZoomed ? 'none' : '90vh'}
        //                     objectFit="contain"
        //                     transform={`scale(${zoomLevel})`}
        //                     transition="transform 0.3s ease"
        //                     draggable={false}
        //                     userSelect="none"
        //                 />

        //                 {/* Navigation Arrows */}
        //                 {images.length > 1 && !isMobile && (
        //                     <>
        //                         <IconButton
        //                             aria-label="Previous image"
        //                             icon={<FaChevronLeft />}
        //                             position="absolute"
        //                             left={4}
        //                             top="50%"
        //                             transform="translateY(-50%)"
        //                             bg="blackAlpha.600"
        //                             color="white"
        //                             _hover={{ bg: 'blackAlpha.800' }}
        //                             size="lg"
        //                             borderRadius="full"
        //                             onClick={(e) => {
        //                                 e.stopPropagation();
        //                                 onPrevious();
        //                             }}
        //                         />
        //                         <IconButton
        //                             aria-label="Next image"
        //                             icon={<FaChevronRight />}
        //                             position="absolute"
        //                             right={4}
        //                             top="50%"
        //                             transform="translateY(-50%)"
        //                             bg="blackAlpha.600"
        //                             color="white"
        //                             _hover={{ bg: 'blackAlpha.800' }}
        //                             size="lg"
        //                             borderRadius="full"
        //                             onClick={(e) => {
        //                                 e.stopPropagation();
        //                                 onNext();
        //                             }}
        //                         />
        //                     </>
        //                 )}
        //             </Box>

        //             {/* Bottom Controls */}
        //             <VStack
        //                 position="absolute"
        //                 bottom={4}
        //                 left="50%"
        //                 transform="translateX(-50%)"
        //                 spacing={4}
        //                 zIndex={100}
        //             >
        //                 {/* Zoom Controls */}
        //                 <HStack
        //                     bg="blackAlpha.700"
        //                     borderRadius="full"
        //                     p={2}
        //                     spacing={2}
        //                 >
        //                     <IconButton
        //                         aria-label="Zoom out"
        //                         icon={<FaSearchMinus />}
        //                         size="sm"
        //                         variant="ghost"
        //                         color="white"
        //                         onClick={(e) => {
        //                             e.stopPropagation();
        //                             handleZoomOut();
        //                         }}
        //                         isDisabled={zoomLevel <= 1}
        //                     />
        //                     <Text color="white" fontSize="sm" minW="60px" textAlign="center">
        //                         {Math.round(zoomLevel * 100)}%
        //                     </Text>
        //                     <IconButton
        //                         aria-label="Zoom in"
        //                         icon={<FaSearchPlus />}
        //                         size="sm"
        //                         variant="ghost"
        //                         color="white"
        //                         onClick={(e) => {
        //                             e.stopPropagation();
        //                             handleZoomIn();
        //                         }}
        //                         isDisabled={zoomLevel >= 4}
        //                     />
        //                     <IconButton
        //                         aria-label={isZoomed ? "Fit to screen" : "Zoom to fit"}
        //                         icon={isZoomed ? <FaCompress /> : <FaExpand />}
        //                         size="sm"
        //                         variant="ghost"
        //                         color="white"
        //                         onClick={(e) => {
        //                             e.stopPropagation();
        //                             onToggleZoom();
        //                         }}
        //                     />
        //                 </HStack>

        //                 {/* Image Counter */}
        //                 {images.length > 1 && (
        //                     <Text
        //                         color="white"
        //                         fontSize="sm"
        //                         bg="blackAlpha.700"
        //                         px={3}
        //                         py={1}
        //                         borderRadius="full"
        //                     >
        //                         {currentIndex + 1} / {images.length}
        //                     </Text>
        //                 )}

        //                 {/* Mobile Navigation */}
        //                 {isMobile && images.length > 1 && (
        //                     <HStack spacing={2}>
        //                         <IconButton
        //                             aria-label="Previous image"
        //                             icon={<FaChevronLeft />}
        //                             bg="blackAlpha.700"
        //                             color="white"
        //                             _hover={{ bg: 'blackAlpha.800' }}
        //                             size="md"
        //                             borderRadius="full"
        //                             onClick={(e) => {
        //                                 e.stopPropagation();
        //                                 onPrevious();
        //                             }}
        //                         />
        //                         <IconButton
        //                             aria-label="Next image"
        //                             icon={<FaChevronRight />}
        //                             bg="blackAlpha.700"
        //                             color="white"
        //                             _hover={{ bg: 'blackAlpha.800' }}
        //                             size="md"
        //                             borderRadius="full"
        //                             onClick={(e) => {
        //                                 e.stopPropagation();
        //                                 onNext();
        //                             }}
        //                         />
        //                     </HStack>
        //                 )}
        //             </VStack>
        //         </ModalBody>
        //     </ModalContent>
        // </Modal>
    );
};

export default ImageModal;
