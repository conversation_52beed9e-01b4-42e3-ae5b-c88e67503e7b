"use client"
import { <PERSON>, <PERSON>ton, createList<PERSON>ollection, Field, Heading, HStack, Icon, RadioCard, Stack, Tabs, Text } from '@chakra-ui/react'
import Link from 'next/link'
import React from 'react'
import { FaArrowLeft } from 'react-icons/fa'
import FormInputField from '../ui/form/FormInputField'
import { ChakraSelect } from '../ui/select/ChakraSelect'
import FormSelectField from '../ui/form/FormSelectField'

interface FormStepProps {

}

type TypeSell = {
    value: string;
    title: string;
}

const TypeSell: TypeSell[] = [
    {
        value: 'auction',
        title: 'Auction',
    },
    {
        value: 'buy-now',
        title: 'Buy Now',
    },
];

const FormSelling: React.FC<FormStepProps> = () => {
    const category = createListCollection({
        items: [
            {
                value: "sport",
                label: "Sport"
            },
            {
                value: "non-sport",
                label: "Non Sport"
            },
            {
                value: "collectible",
                label: "Collectible"
            },
        ],
        itemToValue: (item) => item.value,
    })

    return (
        <Box mx={"auto"} w="full">
            <HStack alignItems="center" gap={4}>
                <Link href="/">
                    <Icon as={FaArrowLeft} boxSize={4} />
                </Link>
                <Box>
                    <Heading as="div" fontWeight="bold" size="lg" color="gray.800">
                        Selling Items
                    </Heading>
                    <Text as="div" fontSize="sm" color="gray.500">
                        Create a new auction listing to sell your items.
                    </Text>
                </Box>
            </HStack>
            <Stack gap={4} bg="white" p={8} mt={6} borderRadius={8} boxShadow="xs">
                <Heading as="h3" size="md" mb={6} fontWeight="bold">
                    Information Product
                </Heading>
                <RadioCard.Root defaultValue="next">
                    <RadioCard.Label fontWeight="bold" color={"gray.800"}>
                        Type Selling  <Box as="span" color="red.500">*</Box>
                    </RadioCard.Label>
                    <RadioCard.Label mb={2} color={"gray.500"}>
                        Please select the type of selling you want to create.
                    </RadioCard.Label>
                    <HStack align="stretch">
                        {TypeSell.map((item) => (
                            <RadioCard.Item key={item.value} value={item.value}>
                                <RadioCard.ItemHiddenInput />
                                <RadioCard.ItemControl>
                                    <RadioCard.ItemText>{item.title}</RadioCard.ItemText>
                                    <RadioCard.ItemIndicator />
                                </RadioCard.ItemControl>
                            </RadioCard.Item>
                        ))}
                    </HStack>
                </RadioCard.Root>
                <FormInputField
                    label="Item Name"
                    description='Enter the name of the item you want to sell.'
                    placeholder="Example: Vintage Watch"
                    required
                />
                <FormSelectField
                    label={"Category"}
                    required
                    placeholder={"Select Category"}
                    collection={category}
                    defaultValue={[]}
                    width='full'
                    size='md'
                    onValueChange={(e) => {

                    }}
                />
            </Stack>

        </Box>
    )
}

export default FormSelling