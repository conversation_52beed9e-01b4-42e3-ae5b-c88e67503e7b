import { useCallback } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';

interface UseProfileRefreshReturn {
  refreshProfile: () => Promise<boolean>;
  isLoading: boolean;
}

export const useProfileRefresh = (): UseProfileRefreshReturn => {
  const { data: session, update } = useSession();

  const refreshProfile = useCallback(async (): Promise<boolean> => {
    if (!session?.accessToken) {
      console.warn('❌ Cannot refresh profile: no access token');
      return false;
    }

    try {
      console.log('🔄 Refreshing profile from API...');

      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
        {
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (data.status && data.data?.user) {
        console.log('✅ Profile data received:', data.data.user);

        // Trigger session update with fresh profile data
        await update({
          user: {
            ...session.user,
            ...data.data.user,
          },
        });

        console.log('✅ Session updated with fresh profile data');
        return true;
      } else {
        console.error('❌ Invalid response from profile API:', data);
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to refresh profile:', error);
      return false;
    }
  }, [session, update]);

  return {
    refreshProfile,
    isLoading: false, // We don't track loading state here
  };
};

// Hook for triggering profile refresh on specific events
export const useProfileRefreshTriggers = () => {
  const { refreshProfile } = useProfileRefresh();

  // Refresh on page focus (when user returns to tab)
  const setupFocusRefresh = useCallback(() => {
    const handleFocus = () => {
      console.log('👁️ Page focused - refreshing profile');
      refreshProfile();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refreshProfile]);

  // Refresh on page visibility change
  const setupVisibilityRefresh = useCallback(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('👁️ Page visible - refreshing profile');
        refreshProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [refreshProfile]);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    console.log('🔄 Manual profile refresh triggered');
    return await refreshProfile();
  }, [refreshProfile]);

  return {
    setupFocusRefresh,
    setupVisibilityRefresh,
    manualRefresh,
    refreshProfile,
  };
};

// Simple hook for components that need to refresh profile
export const useSimpleProfileRefresh = () => {
  const { data: session, update } = useSession();

  const refresh = useCallback(async () => {
    if (!session?.accessToken) return false;

    try {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
        {
          headers: { 'Authorization': `Bearer ${session.accessToken}` },
        }
      );

      if (data.status && data.data?.user) {
        await update({ user: { ...session.user, ...data.data.user } });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Profile refresh failed:', error);
      return false;
    }
  }, [session, update]);

  return refresh;
};
