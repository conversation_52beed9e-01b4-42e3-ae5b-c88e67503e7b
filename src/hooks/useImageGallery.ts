import { useState, useCallback, useEffect } from 'react';
import { ProductImage } from '@/types/product';

interface UseImageGalleryProps {
    images: ProductImage[];
    initialIndex?: number;
}

interface UseImageGalleryReturn {
    currentIndex: number;
    currentImage: ProductImage | null;
    isModalOpen: boolean;
    isZoomed: boolean;
    zoomLevel: number;
    goToNext: () => void;
    goToPrevious: () => void;
    goToIndex: (index: number) => void;
    openModal: (index?: number) => void;
    closeModal: () => void;
    toggleZoom: () => void;
    setZoomLevel: (level: number) => void;
    resetZoom: () => void;
}

export const useImageGallery = ({ 
    images, 
    initialIndex = 0 
}: UseImageGalleryProps): UseImageGalleryReturn => {
    const [currentIndex, setCurrentIndex] = useState(initialIndex);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isZoomed, setIsZoomed] = useState(false);
    const [zoomLevel, setZoomLevel] = useState(1);

    const currentImage = images[currentIndex] || null;

    const goToNext = useCallback(() => {
        setCurrentIndex((prev) => (prev + 1) % images.length);
    }, [images.length]);

    const goToPrevious = useCallback(() => {
        setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
    }, [images.length]);

    const goToIndex = useCallback((index: number) => {
        if (index >= 0 && index < images.length) {
            setCurrentIndex(index);
        }
    }, [images.length]);

    const openModal = useCallback((index?: number) => {
        if (index !== undefined) {
            setCurrentIndex(index);
        }
        setIsModalOpen(true);
    }, []);

    const closeModal = useCallback(() => {
        setIsModalOpen(false);
        resetZoom();
    }, []);

    const toggleZoom = useCallback(() => {
        setIsZoomed(!isZoomed);
        setZoomLevel(isZoomed ? 1 : 2);
    }, [isZoomed]);

    const resetZoom = useCallback(() => {
        setIsZoomed(false);
        setZoomLevel(1);
    }, []);

    // Keyboard navigation
    useEffect(() => {
        if (!isModalOpen) return;

        const handleKeyDown = (event: KeyboardEvent) => {
            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    goToPrevious();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    goToNext();
                    break;
                case 'Escape':
                    event.preventDefault();
                    closeModal();
                    break;
                case ' ':
                    event.preventDefault();
                    toggleZoom();
                    break;
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [isModalOpen, goToNext, goToPrevious, closeModal, toggleZoom]);

    // Reset zoom when changing images
    useEffect(() => {
        resetZoom();
    }, [currentIndex, resetZoom]);

    return {
        currentIndex,
        currentImage,
        isModalOpen,
        isZoomed,
        zoomLevel,
        goToNext,
        goToPrevious,
        goToIndex,
        openModal,
        closeModal,
        toggleZoom,
        setZoomLevel,
        resetZoom
    };
};
