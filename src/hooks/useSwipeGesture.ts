import { useCallback, useRef, useState } from 'react';

interface UseSwipeGestureProps {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
    threshold?: number;
}

interface SwipeState {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    isSwiping: boolean;
}

export const useSwipeGesture = ({
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50
}: UseSwipeGestureProps) => {
    const [swipeState, setSwipeState] = useState<SwipeState>({
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0,
        isSwiping: false
    });

    const handleTouchStart = useCallback((event: React.TouchEvent) => {
        const touch = event.touches[0];
        setSwipeState({
            startX: touch.clientX,
            startY: touch.clientY,
            endX: touch.clientX,
            endY: touch.clientY,
            isSwiping: true
        });
    }, []);

    const handleTouchMove = useCallback((event: React.TouchEvent) => {
        if (!swipeState.isSwiping) return;
        
        const touch = event.touches[0];
        setSwipeState(prev => ({
            ...prev,
            endX: touch.clientX,
            endY: touch.clientY
        }));
    }, [swipeState.isSwiping]);

    const handleTouchEnd = useCallback(() => {
        if (!swipeState.isSwiping) return;

        const deltaX = swipeState.endX - swipeState.startX;
        const deltaY = swipeState.endY - swipeState.startY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        // Determine if it's a horizontal or vertical swipe
        if (absDeltaX > absDeltaY && absDeltaX > threshold) {
            // Horizontal swipe
            if (deltaX > 0) {
                onSwipeRight?.();
            } else {
                onSwipeLeft?.();
            }
        } else if (absDeltaY > absDeltaX && absDeltaY > threshold) {
            // Vertical swipe
            if (deltaY > 0) {
                onSwipeDown?.();
            } else {
                onSwipeUp?.();
            }
        }

        setSwipeState(prev => ({
            ...prev,
            isSwiping: false
        }));
    }, [swipeState, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

    // Mouse events for desktop
    const handleMouseDown = useCallback((event: React.MouseEvent) => {
        setSwipeState({
            startX: event.clientX,
            startY: event.clientY,
            endX: event.clientX,
            endY: event.clientY,
            isSwiping: true
        });
    }, []);

    const handleMouseMove = useCallback((event: React.MouseEvent) => {
        if (!swipeState.isSwiping) return;
        
        setSwipeState(prev => ({
            ...prev,
            endX: event.clientX,
            endY: event.clientY
        }));
    }, [swipeState.isSwiping]);

    const handleMouseUp = useCallback(() => {
        if (!swipeState.isSwiping) return;

        const deltaX = swipeState.endX - swipeState.startX;
        const deltaY = swipeState.endY - swipeState.startY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        if (absDeltaX > absDeltaY && absDeltaX > threshold) {
            if (deltaX > 0) {
                onSwipeRight?.();
            } else {
                onSwipeLeft?.();
            }
        } else if (absDeltaY > absDeltaX && absDeltaY > threshold) {
            if (deltaY > 0) {
                onSwipeDown?.();
            } else {
                onSwipeUp?.();
            }
        }

        setSwipeState(prev => ({
            ...prev,
            isSwiping: false
        }));
    }, [swipeState, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

    return {
        swipeHandlers: {
            onTouchStart: handleTouchStart,
            onTouchMove: handleTouchMove,
            onTouchEnd: handleTouchEnd,
            onMouseDown: handleMouseDown,
            onMouseMove: handleMouseMove,
            onMouseUp: handleMouseUp,
        },
        isSwiping: swipeState.isSwiping
    };
};
