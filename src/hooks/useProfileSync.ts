import { useCallback, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import { logSecurityEvent } from '@/utils/session-security';

interface UseProfileSyncOptions {
  onProfileUpdate?: (profile: any) => void;
  onError?: (error: Error) => void;
  enableOnPageLoad?: boolean;  // default true - fetch on page load/refresh
  enableOnNavigation?: boolean; // default true - fetch on navigation
}

interface UseProfileSyncReturn {
  syncProfile: () => Promise<boolean>;
  isLoading: boolean;
  lastSyncTime: Date | null;
  syncCount: number;
  forceSync: () => Promise<boolean>;
}

export const useProfileSync = (options: UseProfileSyncOptions = {}): UseProfileSyncReturn => {
  const {
    onProfileUpdate,
    onError,
    enableOnPageLoad = true,
    enableOnNavigation = true
  } = options;

  const { data: session, update } = useSession();
  const isLoadingRef = useRef(false);
  const lastSyncTimeRef = useRef<Date | null>(null);
  const syncCountRef = useRef(0);

  // Sync profile from API
  const syncProfile = useCallback(async (): Promise<boolean> => {
    if (isLoadingRef.current || !session?.accessToken) {
      return false;
    }

    try {
      isLoadingRef.current = true;
      
      console.log('🔄 Syncing profile from API...');

      // Debug token info
      console.log('🔍 Token debug info:', {
        hasToken: !!session.accessToken,
        tokenLength: session.accessToken?.length || 0,
        tokenStart: session.accessToken?.substring(0, 50) + '...' || 'No token',
        tokenParts: session.accessToken?.split('.').length || 0,
        isEncrypted: session.accessToken?.includes(':') || false,
      });

      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
        {
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (data.status && data.data?.user) {
        const profileData = data.data.user;
        
        console.log('✅ Profile data received:', {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          email: profileData.email,
          phoneNumber: profileData.phoneNumber
        });

        // Update session with new profile data
        await update({
          user: {
            ...session.user,
            ...profileData,
          },
        });

        lastSyncTimeRef.current = new Date();
        syncCountRef.current += 1;

        // Log successful sync
        logSecurityEvent({
          type: 'session_refreshed',
          timestamp: new Date(),
          details: { 
            action: 'profile_sync_success',
            syncCount: syncCountRef.current
          }
        });

        // Call callback if provided
        onProfileUpdate?.(profileData);

        console.log('✅ Profile sync completed successfully');
        return true;
      } else {
        console.error('❌ Invalid response from profile API:', data);
        return false;
      }
    } catch (error) {
      console.error('❌ Profile sync failed:', error);

      // Log failed sync
      logSecurityEvent({
        type: 'unauthorized_access',
        timestamp: new Date(),
        details: { 
          action: 'profile_sync_failed',
          error: (error as Error).message 
        }
      });

      // Call error callback if provided
      onError?.(error as Error);

      return false;
    } finally {
      isLoadingRef.current = false;
    }
  }, [session, update, onProfileUpdate, onError]);

  // Force sync (ignores loading state)
  const forceSync = useCallback(async (): Promise<boolean> => {
    isLoadingRef.current = false; // Reset loading state
    return await syncProfile();
  }, [syncProfile]);

  // Sync on page load/refresh when session becomes available
  useEffect(() => {
    if (enableOnPageLoad && session?.accessToken && !lastSyncTimeRef.current) {
      console.log('🚀 Page load profile sync');
      syncProfile();
    }
  }, [session?.accessToken, syncProfile, enableOnPageLoad]);

  // Sync on navigation (when component mounts)
  useEffect(() => {
    if (enableOnNavigation && session?.accessToken) {
      console.log('🔄 Navigation profile sync');
      syncProfile();
    }
  }, [enableOnNavigation]); // Only run on mount

  // Listen for page visibility changes (when user returns to tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && session?.accessToken && !isLoadingRef.current) {
        console.log('👁️ Page visible - syncing profile');
        syncProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [session?.accessToken, syncProfile]);

  // No cleanup needed since we don't use intervals anymore

  return {
    syncProfile,
    isLoading: isLoadingRef.current,
    lastSyncTime: lastSyncTimeRef.current,
    syncCount: syncCountRef.current,
    forceSync,
  };
};

// Hook for manual profile updates (when user changes data)
export const useProfileUpdater = () => {
  const { data: session, update } = useSession();

  const updateProfile = useCallback(async (profileData: any) => {
    if (!session) return false;

    try {
      console.log('📝 Updating profile data:', profileData);

      await update({
        user: {
          ...session.user,
          ...profileData,
        },
      });

      console.log('✅ Profile updated in session');
      return true;
    } catch (error) {
      console.error('❌ Failed to update profile:', error);
      return false;
    }
  }, [session, update]);

  return { updateProfile };
};

// Hook for listening to profile changes
export const useProfileListener = (callback: (profile: any) => void) => {
  const { data: session } = useSession();

  useEffect(() => {
    if (session?.user) {
      callback(session.user);
    }
  }, [
    session?.user?.firstName,
    session?.user?.lastName,
    session?.user?.email,
    session?.user?.phoneNumber,
    session?.user?.image,
    callback
  ]);
};

// Utility to trigger profile refresh from anywhere in the app
export const triggerProfileRefresh = async () => {
  // This will trigger the JWT callback to refresh profile
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('profile-refresh-requested');
    window.dispatchEvent(event);
  }
};

// Global profile refresh listener
export const useGlobalProfileRefresh = (refreshFunction: () => Promise<void>) => {
  useEffect(() => {
    const handleRefreshRequest = () => {
      console.log('🔄 Global profile refresh requested');
      refreshFunction();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('profile-refresh-requested', handleRefreshRequest);
      
      return () => {
        window.removeEventListener('profile-refresh-requested', handleRefreshRequest);
      };
    }
  }, [refreshFunction]);
};
