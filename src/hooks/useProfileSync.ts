import { useCallback, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import { logSecurityEvent } from '@/utils/session-security';

interface UseProfileSyncOptions {
  autoRefreshInterval?: number; // in milliseconds, default 30000 (30 seconds)
  enableAutoRefresh?: boolean;  // default true
  onProfileUpdate?: (profile: any) => void;
  onError?: (error: Error) => void;
}

interface UseProfileSyncReturn {
  syncProfile: () => Promise<boolean>;
  isLoading: boolean;
  lastSyncTime: Date | null;
  syncCount: number;
  forceSync: () => Promise<boolean>;
}

export const useProfileSync = (options: UseProfileSyncOptions = {}): UseProfileSyncReturn => {
  const {
    autoRefreshInterval = 30000,
    enableAutoRefresh = true,
    onProfileUpdate,
    onError
  } = options;

  const { data: session, update } = useSession();
  const isLoadingRef = useRef(false);
  const lastSyncTimeRef = useRef<Date | null>(null);
  const syncCountRef = useRef(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Sync profile from API
  const syncProfile = useCallback(async (): Promise<boolean> => {
    if (isLoadingRef.current || !session?.accessToken) {
      return false;
    }

    try {
      isLoadingRef.current = true;
      
      console.log('🔄 Syncing profile from API...');

      // Debug token info
      console.log('🔍 Token debug info:', {
        hasToken: !!session.accessToken,
        tokenLength: session.accessToken?.length || 0,
        tokenStart: session.accessToken?.substring(0, 50) + '...' || 'No token',
        tokenParts: session.accessToken?.split('.').length || 0,
        isEncrypted: session.accessToken?.includes(':') || false,
      });

      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/profile`,
        {
          headers: {
            'Authorization': `Bearer ${session.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (data.status && data.data?.user) {
        const profileData = data.data.user;
        
        console.log('✅ Profile data received:', {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          email: profileData.email,
          phoneNumber: profileData.phoneNumber
        });

        // Update session with new profile data
        await update({
          user: {
            ...session.user,
            ...profileData,
          },
        });

        lastSyncTimeRef.current = new Date();
        syncCountRef.current += 1;

        // Log successful sync
        logSecurityEvent({
          type: 'session_refreshed',
          timestamp: new Date(),
          details: { 
            action: 'profile_sync_success',
            syncCount: syncCountRef.current
          }
        });

        // Call callback if provided
        onProfileUpdate?.(profileData);

        console.log('✅ Profile sync completed successfully');
        return true;
      } else {
        console.error('❌ Invalid response from profile API:', data);
        return false;
      }
    } catch (error) {
      console.error('❌ Profile sync failed:', error);

      // Log failed sync
      logSecurityEvent({
        type: 'unauthorized_access',
        timestamp: new Date(),
        details: { 
          action: 'profile_sync_failed',
          error: (error as Error).message 
        }
      });

      // Call error callback if provided
      onError?.(error as Error);

      return false;
    } finally {
      isLoadingRef.current = false;
    }
  }, [session, update, onProfileUpdate, onError]);

  // Force sync (ignores loading state)
  const forceSync = useCallback(async (): Promise<boolean> => {
    isLoadingRef.current = false; // Reset loading state
    return await syncProfile();
  }, [syncProfile]);

  // Setup auto-refresh interval
  useEffect(() => {
    if (!enableAutoRefresh || !session?.accessToken) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    console.log(`🔄 Setting up auto-refresh every ${autoRefreshInterval}ms`);

    intervalRef.current = setInterval(() => {
      if (!isLoadingRef.current) {
        console.log('⏰ Auto-refresh triggered');
        syncProfile();
      }
    }, autoRefreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [session?.accessToken, enableAutoRefresh, autoRefreshInterval, syncProfile]);

  // Initial sync when session becomes available
  useEffect(() => {
    if (session?.accessToken && !lastSyncTimeRef.current) {
      console.log('🚀 Initial profile sync');
      syncProfile();
    }
  }, [session?.accessToken, syncProfile]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    syncProfile,
    isLoading: isLoadingRef.current,
    lastSyncTime: lastSyncTimeRef.current,
    syncCount: syncCountRef.current,
    forceSync,
  };
};

// Hook for manual profile updates (when user changes data)
export const useProfileUpdater = () => {
  const { data: session, update } = useSession();

  const updateProfile = useCallback(async (profileData: any) => {
    if (!session) return false;

    try {
      console.log('📝 Updating profile data:', profileData);

      await update({
        user: {
          ...session.user,
          ...profileData,
        },
      });

      console.log('✅ Profile updated in session');
      return true;
    } catch (error) {
      console.error('❌ Failed to update profile:', error);
      return false;
    }
  }, [session, update]);

  return { updateProfile };
};

// Hook for listening to profile changes
export const useProfileListener = (callback: (profile: any) => void) => {
  const { data: session } = useSession();

  useEffect(() => {
    if (session?.user) {
      callback(session.user);
    }
  }, [
    session?.user?.firstName,
    session?.user?.lastName,
    session?.user?.email,
    session?.user?.phoneNumber,
    session?.user?.image,
    callback
  ]);
};

// Utility to trigger profile refresh from anywhere in the app
export const triggerProfileRefresh = async () => {
  // This will trigger the JWT callback to refresh profile
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('profile-refresh-requested');
    window.dispatchEvent(event);
  }
};

// Global profile refresh listener
export const useGlobalProfileRefresh = (refreshFunction: () => Promise<void>) => {
  useEffect(() => {
    const handleRefreshRequest = () => {
      console.log('🔄 Global profile refresh requested');
      refreshFunction();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('profile-refresh-requested', handleRefreshRequest);
      
      return () => {
        window.removeEventListener('profile-refresh-requested', handleRefreshRequest);
      };
    }
  }, [refreshFunction]);
};
