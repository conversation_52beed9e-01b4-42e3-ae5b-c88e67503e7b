import { useSession, signIn, signOut } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';
import axios from 'axios';
import {
  logSecurityEvent,
  SecureSession,
  getSessionStatus
} from '@/utils/session-security';
import { useProfileSync, useGlobalProfileRefresh } from './useProfileSync';

interface UseAuthReturn {
  user: any;
  isLoading: boolean;
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  signInWithGoogle: () => Promise<void>;
  signInWithCredentials: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  updateSession: () => Promise<void>;
  isTokenValid: boolean;
  sessionStatus: ReturnType<typeof getSessionStatus>;
  secureSession: SecureSession;
}

export const useAuth = (): UseAuthReturn => {
  const { data: session, status, update } = useSession();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const isLoading = status === 'loading' || isRefreshing;
  const isAuthenticated = !!session?.user;
  const user = session?.user || null;
  const accessToken = session?.accessToken || null;
  const refreshToken = session?.refreshToken || null;

  // Create secure session wrapper
  const secureSession = new SecureSession(session);
  const sessionStatus = getSessionStatus(session);
  const isTokenValid = secureSession.isValid;

  // Use profile sync hook for automatic updates
  const { forceSync } = useProfileSync({
    autoRefreshInterval: 30000, // 30 seconds
    enableAutoRefresh: isAuthenticated,
    onProfileUpdate: (profile) => {
      console.log('✅ Profile updated via sync:', profile);
    },
    onError: (error) => {
      console.error('❌ Profile sync error:', error);
      if (error.message.includes('401')) {
        logout();
      }
    }
  });

  // Setup global profile refresh listener
  useGlobalProfileRefresh(async () => {
    await forceSync();
  });

  // Sign in with Google
  const signInWithGoogle = useCallback(async () => {
    try {
      await signIn('google', { 
        callbackUrl: '/',
        redirect: true 
      });
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw error;
    }
  }, []);

  // Sign in with credentials
  const signInWithCredentials = useCallback(async (email: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      return result;
    } catch (error) {
      console.error('Credentials sign-in error:', error);
      throw error;
    }
  }, []);

  // Logout
  const logout = useCallback(async () => {
    try {
      await signOut({ 
        callbackUrl: '/auth/login',
        redirect: true 
      });
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }, []);

  // Force refresh user profile from API
  const refreshProfile = useCallback(async () => {
    if (!secureSession.isValid || !secureSession.accessToken) {
      console.warn('Cannot refresh profile: invalid session');
      return;
    }

    try {
      setIsRefreshing(true);

      console.log('🔄 Manual profile refresh requested...');

      // Log security event
      logSecurityEvent({
        type: 'session_refreshed',
        timestamp: new Date(),
        details: { action: 'manual_profile_refresh' }
      });

      // Use the profile sync function
      const success = await forceSync();

      if (success) {
        console.log('✅ Manual profile refresh completed successfully');
      } else {
        console.error('❌ Manual profile refresh failed');
      }
    } catch (error) {
      console.error('❌ Failed to refresh profile:', error);

      // Log security event
      logSecurityEvent({
        type: 'unauthorized_access',
        timestamp: new Date(),
        details: {
          action: 'profile_refresh_failed',
          error: (error as Error).message
        }
      });
    } finally {
      setIsRefreshing(false);
    }
  }, [secureSession, forceSync]);

  // Update session (trigger refresh)
  const updateSession = useCallback(async () => {
    try {
      await update();
    } catch (error) {
      console.error('Failed to update session:', error);
    }
  }, [update]);

  // Note: Profile sync is now handled by useProfileSync hook automatically

  // Setup axios interceptor for automatic token refresh
  useEffect(() => {
    if (!accessToken || !refreshToken) return;

    const interceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (
          error.response?.status === 401 &&
          !originalRequest._retry &&
          refreshToken
        ) {
          originalRequest._retry = true;

          try {
            // Try to refresh token
            const { data } = await axios.post(
              `${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`,
              { refreshToken }
            );

            if (data.status && data.data) {
              // Update session with new tokens
              await update({
                accessToken: data.data.accessToken,
                refreshToken: data.data.refreshToken,
                expiresAt: data.data.expiresAt,
              });

              // Retry original request with new token
              originalRequest.headers.Authorization = `Bearer ${data.data.accessToken}`;
              return axios(originalRequest);
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            await logout();
          }
        }

        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, [accessToken, refreshToken, update, logout]);

  return {
    user,
    isLoading,
    isAuthenticated,
    accessToken,
    refreshToken,
    signInWithGoogle,
    signInWithCredentials,
    logout,
    refreshProfile,
    updateSession,
    isTokenValid,
    sessionStatus,
    secureSession,
  };
};
