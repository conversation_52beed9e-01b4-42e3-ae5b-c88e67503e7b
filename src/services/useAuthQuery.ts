import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { mockMutation, } from "@/utils/axios-mock";
import { toaster } from "@/components/ui/toaster";

interface MutationRegister {
  onError: (error: unknown) => void;
  onSuccess: (data: any) => void;
}

export const MutationRegister = ({
  onError,
  onSuccess,
}: MutationRegister): UseMutationResult<any, Error, any> => {
  const url = `/auth/register`;

  const mutationFn = async (data: any) => {
    return await mockMutation(url, data, "post" );
  };

  const resMutation = useMutation({
    mutationKey: [url],
    mutationFn: mutationFn,
    onError,
    onSuccess,
  }) as UseMutationResult;

  return resMutation;
};
