# Auth API Testing
# Use this file with REST Client extension in VS Code

### Variables
@baseUrl = http://localhost:3001/api/v1
@accessToken = your-access-token-here
@refreshToken = your-refresh-token-here

### 1. Test Login with Credentials
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "emailPhoneNumber": "<EMAIL>",
  "password": "password123"
}

### 2. Test User Registration
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phoneNumber": "(62) 812345678",
  "password": "password123",
  "confirmPassword": "password123"
}

### 3. Test Google OAuth
POST {{baseUrl}}/auth/google
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "<PERSON>",
  "image": "https://lh3.googleusercontent.com/a/example",
  "googleId": "123456789012345678901",
  "accessToken": "google_access_token_here"
}

### 4. Test Token Refresh
POST {{baseUrl}}/auth/refresh
Content-Type: application/json

{
  "refreshToken": "{{refreshToken}}"
}

### 5. Test Get Profile (Protected)
GET {{baseUrl}}/auth/profile
Authorization: Bearer {{accessToken}}

### 6. Test Invalid Token
GET {{baseUrl}}/auth/profile
Authorization: Bearer invalid-token

### 7. Test Missing Authorization Header
GET {{baseUrl}}/auth/profile

### 8. Test Database Connection
GET {{baseUrl}}/check-db

### 9. Test API Documentation
GET {{baseUrl}}/docs
