import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import authService from "../services/auth.service";

class AuthController {
  async login(c: Context) {
    try {
      const body = await c.req.json();
      const { emailPhoneNumber, password } = body;

      const login = await authService.login({ emailPhoneNumber, password });
      if (!login.status) {
        return c.json(login, 400);
      }

      return c.json(successResponse("Login successful", login.data), 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async register(c: Context) {
    try {
      const body = await c.req.json();
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword,
      } = body;

      const register = await authService.register({
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword,
      })

      if (!register.status) {
        return c.json(register, 400);
      }

      return c.json(register, 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async profile(c: Context) {
    try {
      const payload = {
        id: "user-id-from-token",
        firstName: "John",
        lastName: "Doe",
        email: "reiandika10@ga",
        phoneNumber: "(62) 812345678",
      }
      return c.json(payload, 200);
      const user = c.get("user");
      if (!user) {
        return c.json(errorResponse("User not found"), 404);
      }

      return c.json(user, 200);
    } catch (error) {
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new AuthController();
