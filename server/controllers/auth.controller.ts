import { Context } from "hono";
import { errorResponse, successResponse } from "../utils/response.util";
import authService from "../services/auth.service";
// Manual validation functions
const validateLogin = (data: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.emailPhoneNumber || typeof data.emailPhoneNumber !== 'string') {
    errors.push("Email or phone number is required");
  } else if (data.emailPhoneNumber.length > 254) {
    errors.push("Email too long");
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = data.emailPhoneNumber.replace(/[\s\-\(\)]/g, '');
    if (!emailRegex.test(data.emailPhoneNumber) && !phoneRegex.test(cleanPhone)) {
      errors.push("Invalid email or phone number format");
    }
  }

  if (!data.password || typeof data.password !== 'string') {
    errors.push("Password is required");
  } else if (data.password.length < 8) {
    errors.push("Password must be at least 8 characters");
  } else if (data.password.length > 128) {
    errors.push("Password too long");
  }

  return { valid: errors.length === 0, errors };
};

const validateRegister = (data: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.firstName || typeof data.firstName !== 'string') {
    errors.push("First name is required");
  } else if (data.firstName.length > 50) {
    errors.push("First name too long");
  } else if (!/^[a-zA-Z\s]+$/.test(data.firstName)) {
    errors.push("First name can only contain letters");
  }

  if (!data.lastName || typeof data.lastName !== 'string') {
    errors.push("Last name is required");
  } else if (data.lastName.length > 50) {
    errors.push("Last name too long");
  } else if (!/^[a-zA-Z\s]+$/.test(data.lastName)) {
    errors.push("Last name can only contain letters");
  }

  if (!data.email || typeof data.email !== 'string') {
    errors.push("Email is required");
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push("Invalid email format");
  } else if (data.email.length > 254) {
    errors.push("Email too long");
  }

  if (data.phoneNumber && typeof data.phoneNumber === 'string') {
    if (!/^\(\d{2}\)\s\d{8,12}$/.test(data.phoneNumber)) {
      errors.push("Phone number must be in format (XX) XXXXXXXX");
    }
  }

  if (!data.password || typeof data.password !== 'string') {
    errors.push("Password is required");
  } else if (data.password.length < 8) {
    errors.push("Password must be at least 8 characters");
  } else if (data.password.length > 128) {
    errors.push("Password too long");
  } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(data.password)) {
    errors.push("Password must contain uppercase, lowercase, number, and special character");
  }

  if (!data.confirmPassword || data.password !== data.confirmPassword) {
    errors.push("Passwords don't match");
  }

  return { valid: errors.length === 0, errors };
};

const validateGoogleOAuth = (data: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!data.email || typeof data.email !== 'string') {
    errors.push("Email is required");
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push("Invalid email format");
  }

  if (!data.name || typeof data.name !== 'string') {
    errors.push("Name is required");
  } else if (data.name.length > 100) {
    errors.push("Name too long");
  }

  if (!data.googleId || typeof data.googleId !== 'string') {
    errors.push("Google ID is required");
  }

  if (data.image && typeof data.image === 'string') {
    try {
      new URL(data.image);
    } catch {
      errors.push("Invalid image URL");
    }
  }

  return { valid: errors.length === 0, errors };
};

// Security utilities
const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>\"']/g, '');
};

const logSecurityEvent = (c: Context, event: string, details?: any) => {
  const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';
  const userAgent = c.req.header('User-Agent') || 'unknown';

  console.log(`[SECURITY] ${event}:`, {
    ip: clientIp,
    userAgent,
    timestamp: new Date().toISOString(),
    details,
  });
};

class AuthController {
  async login(c: Context) {
    try {
      const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

      // Parse and validate input
      const body = await c.req.json();
      const validationResult = validateLogin(body);

      if (!validationResult.valid) {
        logSecurityEvent(c, 'LOGIN_VALIDATION_FAILED', {
          errors: validationResult.errors,
          ip: clientIp,
        });

        return c.json(
          errorResponse("Invalid input data"),
          400
        );
      }

      const { emailPhoneNumber, password } = body;

      // Sanitize input
      const sanitizedEmail = sanitizeInput(emailPhoneNumber);

      logSecurityEvent(c, 'LOGIN_ATTEMPT', {
        emailPhoneNumber: sanitizedEmail,
        ip: clientIp,
      });

      const login = await authService.login({
        emailPhoneNumber: sanitizedEmail,
        password,
      });

      if (!login.status) {
        logSecurityEvent(c, 'LOGIN_FAILED', {
          emailPhoneNumber: sanitizedEmail,
          reason: login.message,
          ip: clientIp,
        });

        return c.json(login, 401);
      }

      logSecurityEvent(c, 'LOGIN_SUCCESS', {
        userId: login.data?.user?.id,
        emailPhoneNumber: sanitizedEmail,
        ip: clientIp,
      });

      // Set secure headers
      c.header('Cache-Control', 'no-store');
      c.header('Pragma', 'no-cache');

      return c.json(successResponse("Login successful", login.data), 200);
    } catch (error) {
      logSecurityEvent(c, 'LOGIN_ERROR', {
        error: (error as Error).message,
        ip: c.req.header('X-Forwarded-For') || 'unknown',
      });

      return c.json(
        errorResponse("Authentication failed"),
        500
      );
    }
  }

  async register(c: Context) {
    try {
      const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

      // Parse and validate input
      const body = await c.req.json();
      const validationResult = validateRegister(body);

      if (!validationResult.valid) {
        logSecurityEvent(c, 'REGISTER_VALIDATION_FAILED', {
          errors: validationResult.errors,
          ip: clientIp,
        });

        return c.json(
          errorResponse("Invalid input data"),
          400
        );
      }

      const { firstName, lastName, email, phoneNumber, password, confirmPassword } = body;

      // Sanitize input
      const sanitizedData = {
        firstName: sanitizeInput(firstName),
        lastName: sanitizeInput(lastName),
        email: sanitizeInput(email),
        phoneNumber: phoneNumber ? sanitizeInput(phoneNumber) : undefined,
        password,
        confirmPassword,
      };

      logSecurityEvent(c, 'REGISTER_ATTEMPT', {
        email: sanitizedData.email,
        ip: clientIp,
      });

      const register = await authService.register(sanitizedData);

      if (!register.status) {
        logSecurityEvent(c, 'REGISTER_FAILED', {
          email: sanitizedData.email,
          reason: register.message,
          ip: clientIp,
        });

        return c.json(register, 400);
      }

      logSecurityEvent(c, 'REGISTER_SUCCESS', {
        userId: register.data?.user?.id,
        email: sanitizedData.email,
        ip: clientIp,
      });

      // Set secure headers
      c.header('Cache-Control', 'no-store');
      c.header('Pragma', 'no-cache');

      return c.json(register, 201);
    } catch (error) {
      logSecurityEvent(c, 'REGISTER_ERROR', {
        error: (error as Error).message,
        ip: c.req.header('X-Forwarded-For') || 'unknown',
      });

      return c.json(
        errorResponse("Registration failed"),
        500
      );
    }
  }

  async googleOAuth(c: Context) {
    try {
      const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

      // Parse and validate input
      const body = await c.req.json();
      const validationResult = validateGoogleOAuth(body);

      if (!validationResult.valid) {
        logSecurityEvent(c, 'GOOGLE_OAUTH_VALIDATION_FAILED', {
          errors: validationResult.errors,
          ip: clientIp,
        });

        return c.json(
          errorResponse("Invalid OAuth data"),
          400
        );
      }

      const { email, name, image, googleId, accessToken } = body;

      logSecurityEvent(c, 'GOOGLE_OAUTH_ATTEMPT', {
        email,
        googleId,
        ip: clientIp,
      });

      const oauthResult = await authService.googleOAuth({
        email,
        name,
        image,
        googleId,
        accessToken,
      });

      if (!oauthResult.status) {
        logSecurityEvent(c, 'GOOGLE_OAUTH_FAILED', {
          email,
          reason: oauthResult.message,
          ip: clientIp,
        });

        return c.json(oauthResult, 400);
      }

      logSecurityEvent(c, 'GOOGLE_OAUTH_SUCCESS', {
        userId: oauthResult.data?.user?.id,
        email,
        ip: clientIp,
      });

      // Set secure headers
      c.header('Cache-Control', 'no-store');
      c.header('Pragma', 'no-cache');

      return c.json(successResponse("Google OAuth successful", oauthResult.data), 200);
    } catch (error) {
      logSecurityEvent(c, 'GOOGLE_OAUTH_ERROR', {
        error: (error as Error).message,
        ip: c.req.header('X-Forwarded-For') || 'unknown',
      });

      return c.json(
        errorResponse("OAuth authentication failed"),
        500
      );
    }
  }

  async refreshToken(c: Context) {
    try {
      const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

      const body = await c.req.json();
      const { refreshToken } = body;

      if (!refreshToken || typeof refreshToken !== 'string') {
        logSecurityEvent(c, 'REFRESH_TOKEN_INVALID_FORMAT', { ip: clientIp });
        return c.json(
          errorResponse("Refresh token is required"),
          400
        );
      }

      logSecurityEvent(c, 'REFRESH_TOKEN_ATTEMPT', { ip: clientIp });

      const result = await authService.refreshToken(refreshToken);

      if (!result.status) {
        logSecurityEvent(c, 'REFRESH_TOKEN_FAILED', {
          reason: result.message,
          ip: clientIp,
        });

        return c.json(result, 401);
      }

      logSecurityEvent(c, 'REFRESH_TOKEN_SUCCESS', {
        userId: result.data?.user?.id,
        ip: clientIp,
      });

      // Set secure headers
      c.header('Cache-Control', 'no-store');
      c.header('Pragma', 'no-cache');

      return c.json(successResponse("Token refreshed successfully", result.data), 200);
    } catch (error) {
      logSecurityEvent(c, 'REFRESH_TOKEN_ERROR', {
        error: (error as Error).message,
        ip: c.req.header('X-Forwarded-For') || 'unknown',
      });

      return c.json(
        errorResponse("Token refresh failed"),
        500
      );
    }
  }

  async profile(c: Context) {
    try {
      const user = c.get("user");
      const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

      if (!user) {
        logSecurityEvent(c, 'PROFILE_ACCESS_NO_USER', { ip: clientIp });
        return c.json(errorResponse("User not found"), 404);
      }

      // Fetch fresh user data from database
      const userProfile = await authService.getUserProfile(user.userId);

      if (!userProfile.status) {
        logSecurityEvent(c, 'PROFILE_FETCH_FAILED', {
          userId: user.userId,
          reason: userProfile.message,
          ip: clientIp,
        });

        return c.json(userProfile, 404);
      }

      logSecurityEvent(c, 'PROFILE_ACCESS_SUCCESS', {
        userId: user.userId,
        ip: clientIp,
      });

      // Set secure headers
      c.header('Cache-Control', 'no-store');
      c.header('Pragma', 'no-cache');

      return c.json(successResponse("Profile retrieved successfully", userProfile.data), 200);
    } catch (error) {
      logSecurityEvent(c, 'PROFILE_ACCESS_ERROR', {
        error: (error as Error).message,
        ip: c.req.header('X-Forwarded-For') || 'unknown',
      });

      return c.json(
        errorResponse("Failed to retrieve profile"),
        500
      );
    }
  }
}

export default new AuthController();
