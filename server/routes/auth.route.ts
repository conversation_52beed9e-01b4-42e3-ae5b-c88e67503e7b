import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import {
  loginSchema,
  registerSchema,
  googleOAuthSchema,
  refreshTokenSchema,
  authResponseSchema,
  userResponseSchema
} from "../schemas/auth.schema";
import authController from "../controllers/auth.controller";
import { authMiddleware } from "../middlewares/auth";

const authRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

const loginRoute = createRoute({
  method: "post",
  path: "/login",
  request: {
    body: {
      content: {
        "application/json": {
          schema: loginSchema.openapi("LoginSchema", {
            example: {
              emailPhoneNumber: "<EMAIL>",
              password: "password123",
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Login successful",
      content: {
        "application/json": {
          schema: z.object({
            message: z.string().describe("Success message"),
            token: z.string().describe("JWT token for authentication"),
          }),
        }
      }
    },
    400: {
      description: "Registration failed",
    },
  },
});


const registerRoute = createRoute({
  method: "post",
  path: "/register",
  request: {
    body: {
      content: {
        "application/json": {
          schema: registerSchema.openapi("RegisterSchema", {
            example: {
              firstName: "John",
              lastName: "Doe",
              email: "<EMAIL>",
              phoneNumber: "(62) 812345678",
              password: "password123",
              confirmPassword: "password123",
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Registration successful",
      content: {
        "application/json": {
          schema: z.object({
            message: z.string().describe("Success message"),
            reportUrl: z.string().describe("Register success"),
          }),
        }
      }
    },
    400: {
      description: "Registration failed",
    },
  },
});

const googleOAuthRoute = createRoute({
  method: "post",
  path: "/google",
  request: {
    body: {
      content: {
        "application/json": {
          schema: googleOAuthSchema.openapi("GoogleOAuthSchema", {
            example: {
              email: "<EMAIL>",
              name: "John Doe",
              image: "https://example.com/avatar.jpg",
              googleId: "123456789",
              accessToken: "google_access_token"
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Google OAuth successful",
      content: {
        "application/json": {
          schema: authResponseSchema,
        }
      }
    },
    400: {
      description: "Google OAuth failed",
    },
  },
});

const refreshTokenRoute = createRoute({
  method: "post",
  path: "/refresh",
  request: {
    body: {
      content: {
        "application/json": {
          schema: refreshTokenSchema.openapi("RefreshTokenSchema", {
            example: {
              refreshToken: "refresh_token_here"
            },
          }),
        },
      },
      required: true,
    },
  },
  responses: {
    200: {
      description: "Token refreshed successfully",
      content: {
        "application/json": {
          schema: authResponseSchema,
        }
      }
    },
    401: {
      description: "Invalid refresh token",
    },
  },
});

const profileRoute = createRoute({
  method: "get",
  path: "/profile",
  security: [{ bearerAuth: [] }],
  request: {
    headers: z.object({
      Authorization: z.string()
        .regex(/^Bearer [A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$/) // More precise JWT pattern
        .describe("JWT Bearer token required. Example: 'Bearer eyJhbGciOiJIUzI1Ni...'")
    })
  },
  responses: {
    200: {
      description: "Profile retrieved successfully",
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string().describe("Success message"),
            data: z.object({
              user: userResponseSchema,
            }),
          }),
        }
      }
    },
    401: {
      description: "Unauthorized access",
    },
    404: {
      description: "User not found",
    }
  }
});


authRoutes.openapi(registerRoute, authController.register);
authRoutes.openapi(loginRoute, authController.login);
authRoutes.openapi(googleOAuthRoute, authController.googleOAuth);
authRoutes.openapi(refreshTokenRoute, authController.refreshToken);

authRoutes.use("/profile", authMiddleware);
authRoutes.openapi(profileRoute, authController.profile);

export { authRoutes }