import bcrypt from "bcryptjs";
import { prisma } from "../db";
import { RegisterInput } from "../types/auth";
import { errorResponse, successResponse } from "../utils/response.util";
import {
  generateSecureTokens,
  verifySecureToken,
  logSecurityEvent
} from "../utils/security.util";

interface GoogleOAuthInput {
  email: string;
  name: string;
  image?: string;
  googleId: string;
  accessToken?: string;
}

const parseGoogleName = (name: string) => {
  const parts = name.split(" ");
  return {
    firstName: parts[0] || "",
    lastName: parts.slice(1).join(" ") || ""
  };
};

class AuthService {
  async login(body: { emailPhoneNumber: string; password: string }) {
    try {
      const { emailPhoneNumber, password } = body;

      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: emailPhoneNumber },
            { phoneNumber: emailPhoneNumber },
          ],
        },
      });

      const isPasswordValid = await bcrypt.compare(password, user?.password || "");

      if (!user || !isPasswordValid) {
        return errorResponse("Email or Phone number does not exist");
      }

      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        loginMethod: 'credentials'
      });

      // Log successful login
      logSecurityEvent({
        type: 'auth_success',
        userId: user.id,
        ip: 'unknown', // Will be set by middleware
        userAgent: 'unknown',
        details: { method: 'credentials' },
        timestamp: new Date()
      });

      return successResponse("Login successful", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      throw new Error(`Login failed: ${(error as Error).message}`);
    }
  }

  async googleOAuth(body: GoogleOAuthInput) {
    try {
      const { email, name, image, googleId } = body;
      const { firstName, lastName } = parseGoogleName(name);

      // Check if user exists with this Google ID
      let user = await prisma.user.findFirst({
        where: {
          OR: [
            { oauthId: googleId, oauthProvider: "google" },
            { email: email }
          ],
        },
      });

      if (user) {
        // Update existing user with Google info if not already set
        if (!user.oauthId || !user.oauthProvider) {
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              oauthId: googleId,
              oauthProvider: "google",
              image: image || user.image,
            },
          });
        }
      } else {
        // Create new user
        user = await prisma.user.create({
          data: {
            firstName,
            lastName,
            email,
            image,
            oauthId: googleId,
            oauthProvider: "google",
            password: "", // No password for OAuth users
          },
        });
      }

      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        loginMethod: 'google_oauth'
      });

      // Log successful OAuth login
      logSecurityEvent({
        type: 'auth_success',
        userId: user.id,
        ip: 'unknown',
        userAgent: 'unknown',
        details: { method: 'google_oauth' },
        timestamp: new Date()
      });

      return successResponse("Google OAuth successful", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      throw new Error(`Google OAuth failed: ${(error as Error).message}`);
    }
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify plain JWT refresh token
      const payload = await verifySecureToken(refreshToken, true);

      if (!payload || payload.type !== "refresh") {
        return errorResponse("Invalid refresh token");
      }

      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
      });

      if (!user) {
        return errorResponse("User not found");
      }

      if (!user.isActive || user.isBlocked) {
        return errorResponse("User account is inactive or blocked");
      }

      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        refreshedAt: new Date().toISOString()
      });

      // Log token refresh
      logSecurityEvent({
        type: 'token_refresh',
        userId: user.id,
        ip: 'unknown',
        userAgent: 'unknown',
        details: { jti: payload.jti },
        timestamp: new Date()
      });

      return successResponse("Token refreshed successfully", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      // Log failed refresh attempt
      logSecurityEvent({
        type: 'auth_failure',
        ip: 'unknown',
        userAgent: 'unknown',
        details: {
          reason: 'refresh_token_invalid',
          error: (error as Error).message
        },
        timestamp: new Date()
      });

      return errorResponse("Invalid or expired refresh token");
    }
  }

  async getProfile(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phoneNumber: true,
          image: true,
          oauthProvider: true,
          oauthId: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        return errorResponse("User not found");
      }

      return successResponse("Profile retrieved successfully", user);
    } catch (error) {
      throw new Error(`Get profile failed: ${(error as Error).message}`);
    }
  }

  async register(body: RegisterInput) {
    try {
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword
      } = body;

      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            { phoneNumber: phoneNumber },
          ],
        },
      });

      if (userExists) {
        return errorResponse("User already exists", {
          email: ["User with this email already exists"],
          phoneNumber: ["User with this phone number already exists"],
        });
      }

      if (password !== confirmPassword) {
        return errorResponse("Passwords do not match", {
          password: ["Passwords do not match"],
          confirmPassword: ["Passwords do not match"],
        });
      }

      const hashedPassword = await bcrypt.hash(password, 10)

      const newUser = await prisma.user.create({
        data: {
          firstName,
          lastName,
          email,
          phoneNumber,
          password: hashedPassword,
        },
      });

      if (!newUser) {
        return errorResponse("Failed to create user");
      }

      return successResponse("Register successfully")
    } catch (error) {
      throw new Error(`Report generation failed: ${(error as Error).message}`);
    }
  }
}

export default new AuthService();
