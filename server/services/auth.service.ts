import bcrypt from "bcryptjs";
import { prisma } from "../db";
import { RegisterInput } from "../types/auth";
import { errorResponse, successResponse } from "../utils/response.util";
import { sign } from 'hono/jwt';

class AuthService {
  async login(body: { emailPhoneNumber: string; password: string }) {
    try {
      const { emailPhoneNumber, password } = body;

      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: emailPhoneNumber },
            { phoneNumber: emailPhoneNumber },
          ],
        },
      });

      const isPasswordValid = await bcrypt.compare(password, user?.password || "");

      if (!user || !isPasswordValid) {
        return errorResponse("Email or Phone number does not exist");
      }

      const payload = {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        exp: Math.floor(Date.now() / 1000) + 60 * 5,
      }
      const secret = process.env.JWT_SECRET || 'keyyyyy';
      const token = await sign(payload, secret)

      return successResponse("Login successful", {
        accessToken: token,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
        expitedAt: payload.exp,
      });
    } catch (error) {
      throw new Error(`Login failed: ${(error as Error).message}`);
    }
  }

  async register(body: RegisterInput) {
    try {
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword
      } = body;

      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            { phoneNumber: phoneNumber },
          ],
        },
      });

      if (userExists) {
        return errorResponse("User already exists", {
          email: ["User with this email already exists"],
          phoneNumber: ["User with this phone number already exists"],
        });
      }

      if (password !== confirmPassword) {
        return errorResponse("Passwords do not match", {
          password: ["Passwords do not match"],
          confirmPassword: ["Passwords do not match"],
        });
      }

      const hashedPassword = await bcrypt.hash(password, 10)

      const newUser = await prisma.user.create({
        data: {
          firstName,
          lastName,
          email,
          phoneNumber,
          password: hashedPassword,
        },
      });

      if (!newUser) {
        return errorResponse("Failed to create user");
      }

      return successResponse("Register successfully")
    } catch (error) {
      throw new Error(`Report generation failed: ${(error as Error).message}`);
    }
  }
}

export default new AuthService();
