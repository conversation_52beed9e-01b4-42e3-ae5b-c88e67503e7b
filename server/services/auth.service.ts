import bcrypt from "bcryptjs";
import { prisma } from "../db";
import { RegisterInput } from "../types/auth";
import { errorResponse, successResponse } from "../utils/response.util";
import { sign, verify } from 'hono/jwt';
import crypto from 'crypto';

// Security utilities
const generateSecureTokens = async (userId: string, additionalClaims: Record<string, any> = {}) => {
  const now = Math.floor(Date.now() / 1000);
  const jti = crypto.randomUUID();

  const accessTokenPayload = {
    userId,
    jti,
    iat: now,
    exp: now + (24 * 60 * 60), // 24 hours
    ...additionalClaims,
  };

  const refreshTokenPayload = {
    userId,
    jti: jti + '_refresh',
    type: "refresh",
    iat: now,
    exp: now + (7 * 24 * 60 * 60), // 7 days
  };

  const accessTokenSecret = process.env.JWT_SECRET || 'keyyyyy';
  const refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'refresh_keyyyyy';

  const accessToken = await sign(accessTokenPayload, accessTokenSecret);
  const refreshToken = await sign(refreshTokenPayload, refreshTokenSecret);

  return {
    accessToken,
    refreshToken,
    expiresAt: (now + (24 * 60 * 60)) * 1000, // Convert to milliseconds
    jti,
  };
};

// Verify JWT token
const verifyToken = async (token: string, isRefreshToken = false): Promise<any> => {
  try {
    const secret = isRefreshToken
      ? (process.env.JWT_REFRESH_SECRET || 'refresh_keyyyyy')
      : (process.env.JWT_SECRET || 'keyyyyy');

    const payload = await verify(token, secret);

    if (!payload || typeof payload !== 'object') {
      throw new Error('Invalid token payload');
    }

    if (isRefreshToken && payload.type !== 'refresh') {
      throw new Error('Invalid refresh token type');
    }

    return payload;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
};

interface GoogleOAuthInput {
  email: string;
  name: string;
  image?: string;
  googleId: string;
  accessToken?: string;
}

class AuthService {
  async login(body: { emailPhoneNumber: string; password: string }) {
    try {
      const { emailPhoneNumber, password } = body;

      // Find user by email or phone
      const user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: emailPhoneNumber },
            { phoneNumber: emailPhoneNumber },
          ],
        },
      });

      if (!user) {
        return errorResponse("Invalid credentials");
      }

      // Check if user is active and not blocked
      if (!user.isActive || user.isBlocked) {
        return errorResponse("Account is inactive or blocked");
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password || "");
      if (!isPasswordValid) {
        return errorResponse("Invalid credentials");
      }

      // Generate secure tokens
      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        loginMethod: 'credentials'
      });

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLogin: new Date() },
      });

      return successResponse("Login successful", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
          isActive: user.isActive,
          isBlocked: user.isBlocked,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      console.error('Login error:', error);
      return errorResponse("Authentication failed");
    }
  }

  async register(body: RegisterInput) {
    try {
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        password,
        confirmPassword
      } = body;

      // Check if user already exists
      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            ...(phoneNumber ? [{ phoneNumber: phoneNumber }] : []),
          ],
        },
      });

      if (userExists) {
        if (userExists.email === email) {
          return errorResponse("User with this email already exists");
        }
        if (userExists.phoneNumber === phoneNumber) {
          return errorResponse("User with this phone number already exists");
        }
      }

      // Validate password match
      if (password !== confirmPassword) {
        return errorResponse("Passwords do not match");
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12); // Increased rounds for security

      // Create new user
      const newUser = await prisma.user.create({
        data: {
          firstName,
          lastName,
          email,
          phoneNumber: phoneNumber || null,
          password: hashedPassword,
          isActive: true,
          isBlocked: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      if (!newUser) {
        return errorResponse("Failed to create user");
      }

      // Generate tokens for immediate login
      const tokens = await generateSecureTokens(newUser.id, {
        email: newUser.email,
        loginMethod: 'registration'
      });

      return successResponse("Registration successful", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: newUser.id,
          firstName: newUser.firstName,
          lastName: newUser.lastName,
          email: newUser.email,
          phoneNumber: newUser.phoneNumber,
          image: newUser.image,
          isActive: newUser.isActive,
          isBlocked: newUser.isBlocked,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      console.error('Registration error:', error);
      return errorResponse("Registration failed");
    }
  }

  async googleOAuth(body: GoogleOAuthInput) {
    try {
      const { email, name, image, googleId, accessToken } = body;

      // Split name into first and last name
      const nameParts = name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Check if user exists
      let user = await prisma.user.findFirst({
        where: {
          OR: [
            { email: email },
            { oauthId: googleId, oauthProvider: 'google' },
          ],
        },
      });

      if (user) {
        // Update existing user with Google info if needed
        if (!user.oauthId || !user.oauthProvider) {
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              oauthId: googleId,
              oauthProvider: 'google',
              image: image || user.image,
              lastLogin: new Date(),
              updatedAt: new Date(),
            },
          });
        } else {
          // Just update last login
          user = await prisma.user.update({
            where: { id: user.id },
            data: {
              lastLogin: new Date(),
              image: image || user.image,
            },
          });
        }
      } else {
        // Create new user
        user = await prisma.user.create({
          data: {
            firstName,
            lastName,
            email,
            image,
            oauthId: googleId,
            oauthProvider: 'google',
            isActive: true,
            isBlocked: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            lastLogin: new Date(),
          },
        });
      }

      // Check if user is active
      if (!user.isActive || user.isBlocked) {
        return errorResponse("Account is inactive or blocked");
      }

      // Generate tokens
      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        loginMethod: 'google_oauth'
      });

      return successResponse("Google OAuth successful", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
          isActive: user.isActive,
          isBlocked: user.isBlocked,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      console.error('Google OAuth error:', error);
      return errorResponse("Google OAuth failed");
    }
  }

  async refreshToken(refreshToken: string) {
    try {
      // Verify refresh token
      const payload = await verifyToken(refreshToken, true);

      if (!payload || payload.type !== "refresh") {
        return errorResponse("Invalid refresh token");
      }

      // Check if user exists and is active
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
      });

      if (!user) {
        return errorResponse("User not found");
      }

      if (!user.isActive || user.isBlocked) {
        return errorResponse("User account is inactive or blocked");
      }

      // Generate new tokens
      const tokens = await generateSecureTokens(user.id, {
        email: user.email,
        refreshedAt: new Date().toISOString()
      });

      return successResponse("Token refreshed successfully", {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          image: user.image,
          oauthProvider: user.oauthProvider,
          oauthId: user.oauthId,
          isActive: user.isActive,
          isBlocked: user.isBlocked,
        },
        expiresAt: tokens.expiresAt,
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      return errorResponse("Invalid or expired refresh token");
    }
  }

  async getUserProfile(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phoneNumber: true,
          image: true,
          oauthProvider: true,
          oauthId: true,
          isActive: true,
          isBlocked: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      if (!user) {
        return errorResponse("User not found");
      }

      if (!user.isActive || user.isBlocked) {
        return errorResponse("User account is inactive or blocked");
      }

      return successResponse("Profile retrieved successfully", { user });
    } catch (error) {
      console.error('Get profile error:', error);
      return errorResponse("Failed to retrieve profile");
    }
  }
}

export default new AuthService();
