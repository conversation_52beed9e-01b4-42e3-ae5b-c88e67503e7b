import { z } from "zod";

export const loginSchema = z.object({
  emailPhoneNumber: z.string(),
  password: z.string(),
});

export const registerSchema = z.object({
  firstName: z.string().max(50),
  lastName: z.string().max(50),
  email: z.string().email(),
  phoneNumber: z
    .string()
    .regex(
      /^\([1-9][0-9]{0,3}\)\s?[0-9]{6,13}$/,
      "Nomor handphone tidak valid. Gunakan format seperti: 6281234567890 atau 821234567890"
    ),
  password: z.string().min(8, "Password minimal 8 karakter"),
  confirmPassword: z.string().min(8, "Konfirmasi password minimal 8 karakter")
});

export const googleOAuthSchema = z.object({
  email: z.string().email("Invalid email format"),
  name: z.string().min(1, "Name is required"),
  image: z.string().optional(),
  googleId: z.string().min(1, "Google ID is required"),
  accessToken: z.string().optional(),
});

export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required"),
});

export const userResponseSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  phoneNumber: z.string().nullable(),
  image: z.string().nullable(),
  oauthProvider: z.string().nullable(),
  oauthId: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export const authResponseSchema = z.object({
  status: z.boolean(),
  message: z.string(),
  data: z.object({
    accessToken: z.string(),
    refreshToken: z.string().optional(),
    user: userResponseSchema,
    expiresAt: z.number(),
  }),
});