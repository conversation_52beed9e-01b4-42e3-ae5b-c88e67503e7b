import { z } from "zod";

export const loginSchema = z.object({
  emailPhoneNumber: z.string(),
  password: z.string(),
});

export const registerSchema = z.object({
  firstName: z.string().max(50),
  lastName: z.string().max(50),
  email: z.string().email(),
  phoneNumber: z
    .string()
    .regex(
      /^\([1-9][0-9]{0,3}\)\s?[0-9]{6,13}$/,
      "Nomor handphone tidak valid. Gunakan format seperti: 6281234567890 atau 821234567890"
    ),
  password: z.string().min(8, "Password minimal 8 karakter"),
  confirmPassword: z.string().min(8, "Konfirmasi password minimal 8 karakter")
});