import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { prisma } from '../db';
import { errorResponse } from '../utils/response.util';
import crypto from 'crypto';

// OWASP Security Headers
const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': "default-src 'self'",
  'X-Permitted-Cross-Domain-Policies': 'none',
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
};

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number; blocked: boolean }>();

// Security utilities
const getClientFingerprint = (c: Context): string => {
  const userAgent = c.req.header('User-Agent') || '';
  const xForwardedFor = c.req.header('X-Forwarded-For') || '';
  const xRealIp = c.req.header('X-Real-IP') || '';
  const acceptLanguage = c.req.header('Accept-Language') || '';

  const fingerprint = userAgent + xForwardedFor + xRealIp + acceptLanguage;
  return crypto.createHash('sha256').update(fingerprint).digest('hex').slice(0, 16);
};

const checkRateLimit = (identifier: string, maxRequests = 100, windowMs = 15 * 60 * 1000): boolean => {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  // Check if currently blocked
  if (record?.blocked && now < record.resetTime) {
    return false;
  }

  // Reset or create new record
  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs, blocked: false });
    return true;
  }

  // Check if limit exceeded
  if (record.count >= maxRequests) {
    record.blocked = true;
    record.resetTime = now + (60 * 60 * 1000); // 1 hour block
    return false;
  }

  record.count++;
  return true;
};

const validateTokenStructure = (token: string): boolean => {
  // Basic JWT structure validation
  const parts = token.split('.');
  if (parts.length !== 3) return false;

  try {
    // Validate base64 encoding
    JSON.parse(Buffer.from(parts[1], 'base64').toString());
    return true;
  } catch {
    return false;
  }
};

const sanitizeHeaders = (c: Context): void => {
  // Add security headers
  Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
    c.header(key, value);
  });
};

// Enhanced auth middleware dengan OWASP compliance
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    // Add security headers
    sanitizeHeaders(c);

    const authHeader = c.req.header('Authorization');
    const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';
    const userAgent = c.req.header('User-Agent') || '';

    // Rate limiting check
    const clientFingerprint = getClientFingerprint(c);
    if (!checkRateLimit(clientFingerprint)) {
      console.warn(`Rate limit exceeded for client: ${clientFingerprint} from IP: ${clientIp}`);
      return c.json(
        errorResponse('Too many requests. Please try again later.'),
        429
      );
    }

    // Validate authorization header
    if (!authHeader) {
      console.warn(`Missing authorization header from IP: ${clientIp}`);
      return c.json(
        errorResponse('Authorization header is missing'),
        401
      );
    }

    // Validate header format
    const authParts = authHeader.split(' ');
    if (authParts.length !== 2 || authParts[0] !== 'Bearer') {
      console.warn(`Invalid authorization format from IP: ${clientIp}`);
      return c.json(
        errorResponse('Invalid token format. Use: Bearer <token>'),
        401
      );
    }

    const token = authParts[1];

    // Validate token structure
    if (!validateTokenStructure(token)) {
      console.warn(`Invalid token structure from IP: ${clientIp}`);
      return c.json(
        errorResponse('Invalid token structure'),
        401
      );
    }

    // Verify JWT token
    const secret = process.env.JWT_SECRET || 'keyyyyy';
    const payload = await verify(token, secret) as any;

    if (!payload || !payload.userId) {
      console.warn(`Invalid token payload from IP: ${clientIp}`);
      return c.json(
        errorResponse('Invalid token payload'),
        401
      );
    }

    // Check token expiration with buffer
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      console.warn(`Expired token from IP: ${clientIp}`);
      return c.json(
        errorResponse('Token has expired'),
        401
      );
    }

    // Validate user in database
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        isActive: true,
        isBlocked: true,
        lastLogin: true,
      },
    });

    if (!user) {
      console.warn(`User not found for token from IP: ${clientIp}`);
      return c.json(
        errorResponse('User not found'),
        401
      );
    }

    if (!user.isActive || user.isBlocked) {
      console.warn(`Inactive/blocked user attempt from IP: ${clientIp}, userId: ${user.id}`);
      return c.json(
        errorResponse('User account is inactive or blocked'),
        403
      );
    }

    // Log successful authentication
    console.log(`✅ Auth success: User ${user.id} (${user.email}) from IP ${clientIp}`);

    // Update last login (optional, but useful for security)
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    }).catch(() => {
      // Ignore update errors to not block the request
    });

    // Store user data in context
    c.set('user', {
      ...user,
      userId: user.id,
      clientFingerprint,
      clientIp,
      userAgent,
    });

    // Continue to next middleware/controller
    await next();
  } catch (error) {
    const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';

    console.error(`❌ Auth error from IP ${clientIp}:`, error);

    // Log security events
    console.warn(`Security event: Auth failure from IP ${clientIp}: ${(error as Error).message}`);

    return c.json(
      errorResponse('Invalid or expired token'),
      401
    );
  }
};