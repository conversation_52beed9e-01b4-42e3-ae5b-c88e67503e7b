import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { prisma } from '../db';
import { errorResponse } from '../utils/response.util';
import crypto from 'crypto';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security utilities
const getClientFingerprint = (c: Context): string => {
  const userAgent = c.req.header('User-Agent') || '';
  const xForwardedFor = c.req.header('X-Forwarded-For') || '';
  const xRealIp = c.req.header('X-Real-IP') || '';
  const acceptLanguage = c.req.header('Accept-Language') || '';

  const fingerprint = userAgent + xForwardedFor + xRealIp + acceptLanguage;
  return crypto.createHash('sha256').update(fingerprint).digest('hex').slice(0, 16);
};

const checkRateLimit = (identifier: string, maxRequests = 100, windowMs = 15 * 60 * 1000): boolean => {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxRequests) {
    return false;
  }

  record.count++;
  return true;
};

const validateTokenStructure = (token: string): boolean => {
  // Basic JWT structure validation
  const parts = token.split('.');
  if (parts.length !== 3) return false;

  try {
    // Validate base64 encoding
    JSON.parse(Buffer.from(parts[1], 'base64').toString());
    return true;
  } catch {
    return false;
  }
};

// Enhanced auth middleware dengan security features
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';
    const userAgent = c.req.header('User-Agent') || '';

    // Rate limiting check
    const clientFingerprint = getClientFingerprint(c);
    if (!checkRateLimit(clientFingerprint)) {
      return c.json(
        errorResponse('Too many requests. Please try again later.'),
        429
      );
    }

    // Jika tidak ada header
    if (!authHeader) {
      return c.json(
        errorResponse('Unathorized'),
        401
      );
    }

    // Pisahkan "Bearer" dan token
    const [bearer, token] = authHeader.split(' ');

    // Validasi format
    if (bearer !== 'Bearer' || !token) {
      return c.json(
        errorResponse('Invalid token format. Use: Bearer <token>'),
        401
      );
    }

    // Validasi struktur token
    if (!validateTokenStructure(token)) {
      return c.json(
        errorResponse('Invalid token structure'),
        401
      );
    }

    // Verifikasi token
    const secret = process.env.JWT_SECRET || 'keyyyyy';
    const payload = await verify(token, secret) as any;

    if (!payload || !payload.userId) {
      return c.json(
        errorResponse('Invalid token payload'),
        401
      );
    }

    // Check token expiration dengan buffer
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return c.json(
        errorResponse('Token has expired'),
        401
      );
    }

    // Cek apakah user masih ada dan aktif
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        isActive: true,
        isBlocked: true,
        lastLogin: true,
      },
    });

    if (!user) {
      return c.json(
        errorResponse('User not found'),
        401
      );
    }

    if (!user.isActive || user.isBlocked) {
      return c.json(
        errorResponse('User account is inactive or blocked'),
        401
      );
    }

    // Log successful authentication (optional)
    console.log(`Auth success: User ${user.id} from IP ${clientIp}`);

    // Update last seen (optional, but useful for security)
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLogin: new Date(),
      },
    }).catch(() => {
      // Ignore update errors to not block the request
    });

    // Simpan data user dan security info ke context
    c.set('user', {
      ...user,
      userId: user.id, // Explicit userId for consistency
      clientFingerprint,
      clientIp,
      userAgent
    });

    // Lanjut ke controller jika valid
    await next();
  } catch (error) {
    console.error('Auth error:', error);

    // Log security events
    const clientIp = c.req.header('X-Forwarded-For') || c.req.header('X-Real-IP') || 'unknown';
    console.warn(`Auth failure from IP ${clientIp}: ${(error as Error).message}`);

    return c.json(
      errorResponse('Invalid or expired token'),
      401
    );
  }
};