import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';

// Middleware untuk memeriksa Bearer Token
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    console.log('Authorization Header:', c.req);
    
    // Jika tidak ada header
    if (!authHeader) {
      return c.json(
        { success: false, message: 'Authorization header is missing' },
        401
      );
    }

    // Pisahkan "Bearer" dan token
    const [bearer, token] = authHeader.split(' ');

    // Validasi format
    if (bearer !== 'Bearer' || !token) {
      return c.json(
        { success: false, message: 'Invalid token format. Use: Bearer <token>' },
        401
      );
    }

    // Verifikasi token (contoh menggunakan JWT)
    const payload = await verify(token, process.env.JWT_SECRET!);
    
    // Simpan data user ke context
    c.set('user', payload);

    // Lanjut ke controller jika valid
    await next();
  } catch (error) {
    console.error('Auth error:', error);
    return c.json(
      { success: false, message: 'Invalid or expired token' },
      401
    );
  }
};