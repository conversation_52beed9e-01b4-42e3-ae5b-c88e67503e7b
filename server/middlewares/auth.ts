import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { prisma } from '../db';
import { errorResponse } from '../utils/response.util';

// Middleware untuk memeriksa Bearer Token
export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');

    // Jika tidak ada header
    if (!authHeader) {
      return c.json(
        errorResponse('Authorization header is missing'),
        401
      );
    }

    // Pisahkan "Bearer" dan token
    const [bearer, token] = authHeader.split(' ');

    // Validasi format
    if (bearer !== 'Bearer' || !token) {
      return c.json(
        errorResponse('Invalid token format. Use: Bearer <token>'),
        401
      );
    }

    // Verifikasi token
    const secret = process.env.JWT_SECRET || 'keyyyyy';
    const payload = await verify(token, secret) as any;

    if (!payload || !payload.userId) {
      return c.json(
        errorResponse('Invalid token payload'),
        401
      );
    }

    // Cek apakah user masih ada dan aktif
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        isActive: true,
        isBlocked: true,
      },
    });

    if (!user) {
      return c.json(
        errorResponse('User not found'),
        401
      );
    }

    if (!user.isActive || user.isBlocked) {
      return c.json(
        errorResponse('User account is inactive or blocked'),
        401
      );
    }

    // Simpan data user ke context
    c.set('user', { userId: user.id, ...user });

    // Lanjut ke controller jika valid
    await next();
  } catch (error) {
    console.error('Auth error:', error);
    return c.json(
      errorResponse('Invalid or expired token'),
      401
    );
  }
};