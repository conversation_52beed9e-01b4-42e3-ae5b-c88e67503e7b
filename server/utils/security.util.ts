import crypto from 'crypto';
import { sign, verify } from 'hono/jwt';

// Security configuration
const ENCRYPTION_KEY = process.env.JWT_SECRET?.slice(0, 32).padEnd(32, '0') || 'default-key-32-chars-long-here';
const IV_LENGTH = 16;
const TOKEN_SALT = process.env.TOKEN_SALT || 'default-salt';

// Token encryption/decryption
export const encryptToken = (token: string): string => {
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(token, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error("Token encryption failed:", error);
    return token; // Fallback to plain token
  }
};

export const decryptToken = (encryptedToken: string): string => {
  try {
    if (!encryptedToken.includes(':')) {
      return encryptedToken; // Return as-is if not encrypted
    }
    
    const [ivHex, encrypted] = encryptedToken.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY), iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    console.error("Token decryption failed:", error);
    return encryptedToken; // Return as-is if decryption fails
  }
};

// Enhanced JWT generation with additional security
export const generateSecureTokens = async (userId: string, additionalClaims: Record<string, any> = {}) => {
  const now = Math.floor(Date.now() / 1000);
  const jti = crypto.randomUUID(); // Unique token ID
  const tokenFingerprint = crypto.randomBytes(16).toString('hex');
  
  const accessTokenPayload = {
    userId,
    jti,
    fingerprint: tokenFingerprint,
    iat: now,
    exp: now + (24 * 60 * 60), // 24 hours
    ...additionalClaims,
  };
  
  const refreshTokenPayload = {
    userId,
    jti: jti + '_refresh',
    type: "refresh",
    fingerprint: tokenFingerprint,
    iat: now,
    exp: now + (7 * 24 * 60 * 60), // 7 days
  };
  
  const accessTokenSecret = process.env.JWT_SECRET || 'keyyyyy';
  const refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'refresh_keyyyyy';
  
  const accessToken = await sign(accessTokenPayload, accessTokenSecret);
  const refreshToken = await sign(refreshTokenPayload, refreshTokenSecret);
  
  return {
    accessToken: encryptToken(accessToken),
    refreshToken: encryptToken(refreshToken),
    expiresAt: (now + (24 * 60 * 60)) * 1000, // Convert to milliseconds
    tokenFingerprint,
    jti,
  };
};

// Verify and decrypt token
export const verifySecureToken = async (encryptedToken: string, isRefreshToken = false): Promise<any> => {
  try {
    const token = decryptToken(encryptedToken);
    const secret = isRefreshToken 
      ? (process.env.JWT_REFRESH_SECRET || 'refresh_keyyyyy')
      : (process.env.JWT_SECRET || 'keyyyyy');
    
    const payload = await verify(token, secret);
    
    if (!payload || typeof payload !== 'object') {
      throw new Error('Invalid token payload');
    }
    
    // Additional validation for refresh tokens
    if (isRefreshToken && payload.type !== 'refresh') {
      throw new Error('Invalid refresh token type');
    }
    
    return payload;
  } catch (error) {
    console.error('Token verification failed:', error);
    throw new Error('Invalid or expired token');
  }
};

// Create secure hash for data integrity
export const createSecureHash = (data: string, salt?: string): string => {
  const hashSalt = salt || TOKEN_SALT;
  return crypto.createHmac('sha256', hashSalt).update(data).digest('hex');
};

// Validate secure hash
export const validateSecureHash = (data: string, hash: string, salt?: string): boolean => {
  try {
    const expectedHash = createSecureHash(data, salt);
    return crypto.timingSafeEqual(
      Buffer.from(hash, 'hex'),
      Buffer.from(expectedHash, 'hex')
    );
  } catch (error) {
    console.error('Hash validation failed:', error);
    return false;
  }
};

// Generate secure random string
export const generateSecureRandom = (length = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Rate limiting utilities
interface RateLimitRecord {
  count: number;
  resetTime: number;
  blocked: boolean;
}

const rateLimitStore = new Map<string, RateLimitRecord>();

export const checkRateLimit = (
  identifier: string, 
  maxRequests = 100, 
  windowMs = 15 * 60 * 1000,
  blockDuration = 60 * 60 * 1000 // 1 hour block
): { allowed: boolean; remaining: number; resetTime: number } => {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);
  
  // Check if currently blocked
  if (record?.blocked && now < record.resetTime) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    };
  }
  
  // Reset or create new record
  if (!record || now > record.resetTime) {
    const newRecord: RateLimitRecord = {
      count: 1,
      resetTime: now + windowMs,
      blocked: false
    };
    rateLimitStore.set(identifier, newRecord);
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: newRecord.resetTime
    };
  }
  
  // Check if limit exceeded
  if (record.count >= maxRequests) {
    // Block the identifier
    record.blocked = true;
    record.resetTime = now + blockDuration;
    return {
      allowed: false,
      remaining: 0,
      resetTime: record.resetTime
    };
  }
  
  // Increment counter
  record.count++;
  return {
    allowed: true,
    remaining: maxRequests - record.count,
    resetTime: record.resetTime
  };
};

// Clean up expired rate limit records
export const cleanupRateLimitStore = (): void => {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime && !record.blocked) {
      rateLimitStore.delete(key);
    }
  }
};

// Security headers helper
export const getSecurityHeaders = () => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'",
  };
};

// Client fingerprinting
export const generateClientFingerprint = (headers: Record<string, string>): string => {
  const userAgent = headers['user-agent'] || '';
  const acceptLanguage = headers['accept-language'] || '';
  const acceptEncoding = headers['accept-encoding'] || '';
  const xForwardedFor = headers['x-forwarded-for'] || '';
  
  const fingerprint = [userAgent, acceptLanguage, acceptEncoding, xForwardedFor].join('|');
  return crypto.createHash('sha256').update(fingerprint).digest('hex').slice(0, 16);
};

// Audit logging
export interface SecurityEvent {
  type: 'auth_success' | 'auth_failure' | 'token_refresh' | 'suspicious_activity';
  userId?: string;
  ip: string;
  userAgent: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export const logSecurityEvent = (event: SecurityEvent): void => {
  // In production, send to proper logging service
  console.log(`[SECURITY] ${event.type}:`, {
    userId: event.userId,
    ip: event.ip,
    timestamp: event.timestamp.toISOString(),
    details: event.details,
  });
};
