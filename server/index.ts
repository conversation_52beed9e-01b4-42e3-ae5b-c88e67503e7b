import { errorResponse } from "./utils/response.util";
import { OpenAPIHono } from "@hono/zod-openapi";
import { prisma } from "./db";
import { authRoutes } from "./routes/auth.route";
import { enableSwaggerUi, swaggerDocs } from "./docs/swagger";
// import { jwt } from 'hono/jwt'
import { jwt, type JwtVariables } from 'hono/jwt'

type Variables = JwtVariables

const app = new OpenAPIHono<{ Variables: Variables }>().basePath("/api/v1");

// app.use(async (c, next) => {
//   return next();
// });

app.notFound((c) => {
  return c.json(errorResponse("Not Found"), 404);
});

app.route("/auth", authRoutes);

app.get("/check-db", async (c) => {
  try {
    await prisma.$queryRaw`SELECT 1`; 
    return c.json({ status: "ok", db: true });
  } catch (err) {
    console.error("DB Error:", err);
    return c.json({ status: err, db: false }, 500);
  }
});

app.doc("/swagger.json", {
  openapi: "3.0.0",
  info: {
    title: "Service Report API",
    version: "1.0.0",
    description: "API documentation for service reports",
  },
});

enableSwaggerUi({ app, uiPath: "/docs", docPath: "/api/v1/swagger.json" });

app.get("/docs", swaggerDocs);

export { app };
